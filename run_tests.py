#!/usr/bin/env python3
"""
运行测试脚本
"""
import os
import sys
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, description):
    """运行命令并记录结果"""
    logger.info(f"开始执行: {description}")
    logger.info(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} - 成功")
            if result.stdout:
                logger.info(f"输出:\n{result.stdout}")
        else:
            logger.error(f"❌ {description} - 失败")
            if result.stderr:
                logger.error(f"错误:\n{result.stderr}")
            if result.stdout:
                logger.info(f"输出:\n{result.stdout}")
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"❌ {description} - 异常: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("=== AutoInspectAI 测试套件 ===")
    
    # 检查Python环境
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    
    # 测试步骤
    tests = [
        {
            "command": "python -m pytest app/tests/test_classification_engine.py -v",
            "description": "运行分类引擎单元测试",
            "optional": True
        },
        {
            "command": "python scripts/init_classification_models.py",
            "description": "初始化分类模型文件",
            "optional": False
        },
        {
            "command": "python scripts/test_classification.py",
            "description": "测试分类引擎功能",
            "optional": False
        }
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for i, test in enumerate(tests, 1):
        logger.info(f"\n--- 测试 {i}/{total_count}: {test['description']} ---")
        
        success = run_command(test["command"], test["description"])
        
        if success:
            success_count += 1
        elif not test["optional"]:
            logger.error(f"必需测试失败，停止执行")
            break
        else:
            logger.warning(f"可选测试失败，继续执行")
    
    # 总结
    logger.info(f"\n=== 测试总结 ===")
    logger.info(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.warning(f"⚠️  {total_count - success_count} 个测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
