"""
数据库模型定义
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import logging

from app.config.settings import settings

logger = logging.getLogger(__name__)

# 创建基础模型类
Base = declarative_base()

# 创建异步数据库引擎
engine = create_async_engine(
    settings.database_url_async,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    echo=settings.DEBUG
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


class RecognitionTask(Base):
    """识别任务表"""
    __tablename__ = "recognition_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(64), unique=True, nullable=False, index=True)
    image_path = Column(String(255), nullable=False)
    task_type = Column(String(50), nullable=False)
    status = Column(String(20), default='pending', nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)


class RecognitionResult(Base):
    """识别结果表"""
    __tablename__ = "recognition_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(64), nullable=False, index=True)
    recognition_type = Column(String(50), nullable=False)
    result_data = Column(JSON, nullable=False)
    confidence = Column(Float, nullable=False)
    bbox = Column(JSON, nullable=True)  # 边界框坐标
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class VehicleBrand(Base):
    """车辆品牌库"""
    __tablename__ = "vehicle_brands"
    
    id = Column(Integer, primary_key=True, index=True)
    brand_name = Column(String(100), nullable=False, unique=True)
    brand_logo_path = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class VehicleColor(Base):
    """车辆颜色标准表"""
    __tablename__ = "vehicle_colors"
    
    id = Column(Integer, primary_key=True, index=True)
    color_name = Column(String(50), nullable=False, unique=True)
    color_code = Column(String(20), nullable=True)
    rgb_range = Column(JSON, nullable=True)  # RGB颜色范围
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class VehicleType(Base):
    """车辆类型标准表 (GA/T 16.4)"""
    __tablename__ = "vehicle_types"
    
    id = Column(Integer, primary_key=True, index=True)
    type_code = Column(String(20), nullable=False, unique=True)
    type_name = Column(String(100), nullable=False)
    category = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class TaskDetail(Base):
    """识别任务详情表"""
    __tablename__ = "task_details"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(64), nullable=False, index=True)
    image_metadata = Column(JSON, nullable=True)  # 图片元数据
    processing_time = Column(Integer, nullable=True)  # 处理时间(毫秒)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), nullable=False, unique=True)
    config_value = Column(JSON, nullable=False)
    description = Column(Text, nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class ComparisonTask(Base):
    """比对任务表"""
    __tablename__ = "comparison_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(64), unique=True, nullable=False, index=True)
    recognition_task_id = Column(String(64), nullable=False, index=True)
    reference_data = Column(JSON, nullable=False)  # 参考数据
    comparison_types = Column(JSON, nullable=False)  # 比对类型列表
    status = Column(String(20), default='pending', nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)


class ComparisonResult(Base):
    """比对结果表"""
    __tablename__ = "comparison_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(64), nullable=False, index=True)
    comparison_type = Column(String(50), nullable=False)
    match_result = Column(Boolean, nullable=False)
    similarity = Column(Float, nullable=False)
    details = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class InspectorInfo(Base):
    """查验员信息表"""
    __tablename__ = "inspector_info"
    
    id = Column(Integer, primary_key=True, index=True)
    inspector_id = Column(String(50), unique=True, nullable=False)
    inspector_name = Column(String(100), nullable=False)
    face_encoding = Column(JSON, nullable=True)  # 人脸编码特征
    photo_path = Column(String(255), nullable=True)
    department = Column(String(100), nullable=True)
    status = Column(String(20), default='active', nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# 数据库初始化函数
async def init_db():
    """初始化数据库"""
    try:
        async with engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise


# 获取数据库会话
async def get_db():
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


# 数据库依赖注入
from fastapi import Depends

def get_db_dependency():
    """数据库依赖注入"""
    return Depends(get_db)
