"""
图像分类引擎
用于车辆品牌标志识别、车身颜色识别、车辆类型识别
"""
import os
import json
import time
from typing import Dict, Any, List, Optional, Tuple
import logging
import numpy as np
import cv2
from PIL import Image
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from torchvision import models

from app.core.ai_engines.base_engine import TorchBaseEngine
from app.core.utils.exceptions import AIEngineException
from app.config.settings import settings

logger = logging.getLogger(__name__)


class VehicleBrandClassifier(TorchBaseEngine):
    """车辆品牌标志分类器"""
    
    def __init__(self, model_path: str, labels_path: str, device: str = "cpu"):
        """
        初始化品牌分类器
        
        Args:
            model_path: 模型文件路径
            labels_path: 标签文件路径
            device: 计算设备
        """
        super().__init__(model_path, device)
        self.labels_path = labels_path
        self.class_names = []
        self.num_classes = 0
        self.transform = None
        self._setup_transforms()
    
    def _setup_transforms(self):
        """设置图像变换"""
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def load_model(self) -> bool:
        """加载品牌分类模型"""
        try:
            start_time = time.time()
            
            # 加载类别标签
            if not self._load_labels():
                return False
            
            # 创建模型架构
            self.model = models.resnet50(pretrained=False)
            self.model.fc = nn.Linear(self.model.fc.in_features, self.num_classes)
            
            # 加载预训练权重
            if os.path.exists(self.model_path):
                checkpoint = torch.load(self.model_path, map_location=self.torch_device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"加载预训练权重: {self.model_path}")
            else:
                logger.warning(f"模型文件不存在: {self.model_path}，使用随机初始化权重")
            
            self.model.to(self.torch_device)
            self.model.eval()
            
            self.load_time = time.time() - start_time
            self.is_loaded = True
            
            logger.info(f"品牌分类模型加载成功，支持{self.num_classes}个品牌，耗时: {self.load_time:.3f}秒")
            return True
            
        except Exception as e:
            logger.error(f"品牌分类模型加载失败: {str(e)}")
            raise AIEngineException(f"品牌分类模型加载失败: {str(e)}", 2001)
    
    def _load_labels(self) -> bool:
        """加载类别标签"""
        try:
            if os.path.exists(self.labels_path):
                with open(self.labels_path, 'r', encoding='utf-8') as f:
                    labels_data = json.load(f)
                    self.class_names = labels_data.get('class_names', [])
                    self.num_classes = len(self.class_names)
            else:
                # 使用默认品牌列表
                self.class_names = [
                    "奥迪", "宝马", "奔驰", "大众", "丰田", "本田", "日产", "现代",
                    "起亚", "福特", "雪佛兰", "别克", "凯迪拉克", "沃尔沃", "捷豹",
                    "路虎", "保时捷", "法拉利", "兰博基尼", "玛莎拉蒂", "阿斯顿马丁",
                    "宾利", "劳斯莱斯", "迈凯伦", "布加迪", "比亚迪", "吉利", "长城",
                    "奇瑞", "长安", "一汽", "东风", "上汽", "广汽", "北汽", "江淮",
                    "江铃", "五菱", "宝骏", "荣威", "名爵", "传祺", "启辰", "观致",
                    "众泰", "海马", "力帆", "华泰", "中华", "红旗", "解放", "东风商用车"
                ]
                self.num_classes = len(self.class_names)
                logger.warning(f"标签文件不存在，使用默认品牌列表，共{self.num_classes}个品牌")
            
            return True
            
        except Exception as e:
            logger.error(f"加载品牌标签失败: {str(e)}")
            return False
    
    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        预测车辆品牌
        
        Args:
            image: 输入图像
            **kwargs: 其他参数
            
        Returns:
            预测结果
        """
        if not self.is_loaded:
            raise AIEngineException("品牌分类模型未加载", 2001)
        
        if not self.validate_image(image):
            raise AIEngineException("无效的输入图像", 1004)
        
        try:
            start_time = time.time()
            
            # 图像预处理
            processed_image = self._preprocess_brand_image(image)
            
            # 模型推理
            with torch.no_grad():
                outputs = self.model(processed_image)
                probabilities = F.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
                
                # 获取top-k结果
                top_k = kwargs.get('top_k', 5)
                top_probs, top_indices = torch.topk(probabilities, top_k, dim=1)
                
                results = []
                for i in range(top_k):
                    class_idx = top_indices[0][i].item()
                    prob = top_probs[0][i].item()
                    
                    if class_idx < len(self.class_names):
                        results.append({
                            'brand': self.class_names[class_idx],
                            'confidence': float(prob),
                            'class_id': class_idx
                        })
            
            processing_time = time.time() - start_time
            
            return {
                'type': 'vehicle_brand',
                'results': results,
                'best_match': results[0] if results else None,
                'processing_time': processing_time,
                'model_info': {
                    'model_type': 'ResNet50',
                    'num_classes': self.num_classes
                }
            }
            
        except Exception as e:
            logger.error(f"品牌识别失败: {str(e)}")
            raise AIEngineException(f"品牌识别失败: {str(e)}", 2002)
    
    def _preprocess_brand_image(self, image: np.ndarray) -> torch.Tensor:
        """品牌图像预处理"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image)
            
            # 应用变换
            tensor = self.transform(image_pil)
            tensor = tensor.unsqueeze(0)  # 添加batch维度
            
            return tensor.to(self.torch_device)
            
        except Exception as e:
            logger.error(f"品牌图像预处理失败: {str(e)}")
            raise AIEngineException(f"图像预处理失败: {str(e)}", 2001)


class VehicleColorClassifier(TorchBaseEngine):
    """车身颜色分类器"""
    
    def __init__(self, model_path: str, device: str = "cpu"):
        super().__init__(model_path, device)
        self.color_names = [
            "白色", "黑色", "银色", "灰色", "红色", "蓝色", "绿色", "黄色",
            "棕色", "橙色", "紫色", "粉色", "金色", "米色", "深蓝色", "深绿色"
        ]
        self.num_classes = len(self.color_names)
        self._setup_transforms()
    
    def _setup_transforms(self):
        """设置颜色分类的图像变换"""
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def load_model(self) -> bool:
        """加载颜色分类模型"""
        try:
            start_time = time.time()
            
            # 创建模型架构
            self.model = models.resnet34(pretrained=False)
            self.model.fc = nn.Linear(self.model.fc.in_features, self.num_classes)
            
            # 加载预训练权重
            if os.path.exists(self.model_path):
                checkpoint = torch.load(self.model_path, map_location=self.torch_device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"加载颜色分类权重: {self.model_path}")
            else:
                logger.warning(f"颜色分类模型文件不存在: {self.model_path}")
            
            self.model.to(self.torch_device)
            self.model.eval()
            
            self.load_time = time.time() - start_time
            self.is_loaded = True
            
            logger.info(f"颜色分类模型加载成功，支持{self.num_classes}种颜色，耗时: {self.load_time:.3f}秒")
            return True
            
        except Exception as e:
            logger.error(f"颜色分类模型加载失败: {str(e)}")
            raise AIEngineException(f"颜色分类模型加载失败: {str(e)}", 2001)
    
    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """预测车身颜色"""
        if not self.is_loaded:
            raise AIEngineException("颜色分类模型未加载", 2001)
        
        if not self.validate_image(image):
            raise AIEngineException("无效的输入图像", 1004)
        
        try:
            start_time = time.time()
            
            # 图像预处理
            processed_image = self._preprocess_color_image(image)
            
            # 模型推理
            with torch.no_grad():
                outputs = self.model(processed_image)
                probabilities = F.softmax(outputs, dim=1)
                
                # 获取top-3结果
                top_k = min(3, self.num_classes)
                top_probs, top_indices = torch.topk(probabilities, top_k, dim=1)
                
                results = []
                for i in range(top_k):
                    class_idx = top_indices[0][i].item()
                    prob = top_probs[0][i].item()
                    
                    results.append({
                        'color': self.color_names[class_idx],
                        'confidence': float(prob),
                        'class_id': class_idx
                    })
            
            processing_time = time.time() - start_time
            
            return {
                'type': 'vehicle_color',
                'results': results,
                'best_match': results[0] if results else None,
                'processing_time': processing_time,
                'model_info': {
                    'model_type': 'ResNet34',
                    'num_classes': self.num_classes
                }
            }
            
        except Exception as e:
            logger.error(f"颜色识别失败: {str(e)}")
            raise AIEngineException(f"颜色识别失败: {str(e)}", 2002)
    
    def _preprocess_color_image(self, image: np.ndarray) -> torch.Tensor:
        """颜色图像预处理"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image)
            
            # 应用变换
            tensor = self.transform(image_pil)
            tensor = tensor.unsqueeze(0)
            
            return tensor.to(self.torch_device)
            
        except Exception as e:
            logger.error(f"颜色图像预处理失败: {str(e)}")
            raise AIEngineException(f"图像预处理失败: {str(e)}", 2001)


class VehicleTypeClassifier(TorchBaseEngine):
    """车辆类型分类器 (符合GA/T 16.4标准)"""

    def __init__(self, model_path: str, device: str = "cpu"):
        super().__init__(model_path, device)
        # GA/T 16.4 车辆类型标准
        self.vehicle_types = {
            'K11': '小型载客汽车',
            'K12': '中型载客汽车',
            'K13': '大型载客汽车',
            'K21': '小型载货汽车',
            'K22': '中型载货汽车',
            'K23': '重型载货汽车',
            'K31': '小型专项作业车',
            'K32': '中型专项作业车',
            'K33': '重型专项作业车',
            'K41': '轻型牵引车',
            'K42': '中型牵引车',
            'K43': '重型牵引车',
            'M1': '乘用车',
            'M2': '轻型客车',
            'M3': '大型客车',
            'N1': '轻型货车',
            'N2': '中型货车',
            'N3': '重型货车'
        }
        self.type_codes = list(self.vehicle_types.keys())
        self.num_classes = len(self.type_codes)
        self._setup_transforms()

    def _setup_transforms(self):
        """设置车辆类型分类的图像变换"""
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

    def load_model(self) -> bool:
        """加载车辆类型分类模型"""
        try:
            start_time = time.time()

            # 创建模型架构 - 使用更深的网络
            self.model = models.resnet101(pretrained=False)
            self.model.fc = nn.Linear(self.model.fc.in_features, self.num_classes)

            # 加载预训练权重
            if os.path.exists(self.model_path):
                checkpoint = torch.load(self.model_path, map_location=self.torch_device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"加载车辆类型分类权重: {self.model_path}")
            else:
                logger.warning(f"车辆类型分类模型文件不存在: {self.model_path}")

            self.model.to(self.torch_device)
            self.model.eval()

            self.load_time = time.time() - start_time
            self.is_loaded = True

            logger.info(f"车辆类型分类模型加载成功，支持{self.num_classes}种类型，耗时: {self.load_time:.3f}秒")
            return True

        except Exception as e:
            logger.error(f"车辆类型分类模型加载失败: {str(e)}")
            raise AIEngineException(f"车辆类型分类模型加载失败: {str(e)}", 2001)

    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """预测车辆类型"""
        if not self.is_loaded:
            raise AIEngineException("车辆类型分类模型未加载", 2001)

        if not self.validate_image(image):
            raise AIEngineException("无效的输入图像", 1004)

        try:
            start_time = time.time()

            # 图像预处理
            processed_image = self._preprocess_type_image(image)

            # 模型推理
            with torch.no_grad():
                outputs = self.model(processed_image)
                probabilities = F.softmax(outputs, dim=1)

                # 获取top-3结果
                top_k = min(3, self.num_classes)
                top_probs, top_indices = torch.topk(probabilities, top_k, dim=1)

                results = []
                for i in range(top_k):
                    class_idx = top_indices[0][i].item()
                    prob = top_probs[0][i].item()
                    type_code = self.type_codes[class_idx]

                    results.append({
                        'type_code': type_code,
                        'type_name': self.vehicle_types[type_code],
                        'confidence': float(prob),
                        'class_id': class_idx
                    })

            processing_time = time.time() - start_time

            return {
                'type': 'vehicle_type',
                'results': results,
                'best_match': results[0] if results else None,
                'processing_time': processing_time,
                'model_info': {
                    'model_type': 'ResNet101',
                    'num_classes': self.num_classes,
                    'standard': 'GA/T 16.4'
                }
            }

        except Exception as e:
            logger.error(f"车辆类型识别失败: {str(e)}")
            raise AIEngineException(f"车辆类型识别失败: {str(e)}", 2002)

    def _preprocess_type_image(self, image: np.ndarray) -> torch.Tensor:
        """车辆类型图像预处理"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image)

            # 应用变换
            tensor = self.transform(image_pil)
            tensor = tensor.unsqueeze(0)

            return tensor.to(self.torch_device)

        except Exception as e:
            logger.error(f"车辆类型图像预处理失败: {str(e)}")
            raise AIEngineException(f"图像预处理失败: {str(e)}", 2001)


class ClassificationEngineManager:
    """分类引擎管理器"""

    def __init__(self):
        self.brand_classifier = None
        self.color_classifier = None
        self.type_classifier = None
        self.is_initialized = False

    def initialize(self, device: str = "cpu") -> bool:
        """
        初始化所有分类引擎

        Args:
            device: 计算设备

        Returns:
            初始化是否成功
        """
        try:
            # 初始化品牌分类器
            brand_model_path = os.path.join(settings.CLASSIFICATION_MODEL_PATH, "vehicle_brand.pth")
            brand_labels_path = os.path.join(settings.CLASSIFICATION_MODEL_PATH, "brand_labels.json")
            self.brand_classifier = VehicleBrandClassifier(brand_model_path, brand_labels_path, device)

            # 初始化颜色分类器
            color_model_path = os.path.join(settings.CLASSIFICATION_MODEL_PATH, "vehicle_color.pth")
            self.color_classifier = VehicleColorClassifier(color_model_path, device)

            # 初始化类型分类器
            type_model_path = os.path.join(settings.CLASSIFICATION_MODEL_PATH, "vehicle_type.pth")
            self.type_classifier = VehicleTypeClassifier(type_model_path, device)

            # 加载所有模型
            success_count = 0
            total_count = 3

            if self.brand_classifier.load_model():
                success_count += 1
                logger.info("品牌分类器加载成功")

            if self.color_classifier.load_model():
                success_count += 1
                logger.info("颜色分类器加载成功")

            if self.type_classifier.load_model():
                success_count += 1
                logger.info("类型分类器加载成功")

            self.is_initialized = success_count > 0

            logger.info(f"分类引擎初始化完成，成功加载 {success_count}/{total_count} 个模型")
            return self.is_initialized

        except Exception as e:
            logger.error(f"分类引擎初始化失败: {str(e)}")
            return False

    def classify_brand(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """品牌分类"""
        if not self.brand_classifier or not self.brand_classifier.is_loaded:
            raise AIEngineException("品牌分类器未加载", 2001)
        return self.brand_classifier.predict(image, **kwargs)

    def classify_color(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """颜色分类"""
        if not self.color_classifier or not self.color_classifier.is_loaded:
            raise AIEngineException("颜色分类器未加载", 2001)
        return self.color_classifier.predict(image, **kwargs)

    def classify_type(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """类型分类"""
        if not self.type_classifier or not self.type_classifier.is_loaded:
            raise AIEngineException("类型分类器未加载", 2001)
        return self.type_classifier.predict(image, **kwargs)

    def get_status(self) -> Dict[str, Any]:
        """获取所有分类器状态"""
        return {
            'brand_classifier': {
                'loaded': self.brand_classifier.is_loaded if self.brand_classifier else False,
                'model_info': self.brand_classifier.get_model_info() if self.brand_classifier else None
            },
            'color_classifier': {
                'loaded': self.color_classifier.is_loaded if self.color_classifier else False,
                'model_info': self.color_classifier.get_model_info() if self.color_classifier else None
            },
            'type_classifier': {
                'loaded': self.type_classifier.is_loaded if self.type_classifier else False,
                'model_info': self.type_classifier.get_model_info() if self.type_classifier else None
            },
            'is_initialized': self.is_initialized
        }


# 全局分类引擎管理器实例
classification_manager = ClassificationEngineManager()
