"""
数据服务API路由
"""
from fastapi import APIRouter, HTTPException
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/download")
async def download_comparison_data():
    """下载待比对信息"""
    # TODO: 实现数据下载逻辑
    return {"message": "数据下载服务待实现"}


@router.post("/upload")
async def upload_results():
    """上传识别结果"""
    # TODO: 实现结果上传逻辑
    return {"message": "结果上传服务待实现"}
