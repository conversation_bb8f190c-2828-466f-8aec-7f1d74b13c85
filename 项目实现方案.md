# 车辆智能识别系统 - 项目实现方案

## 1. 技术选型说明

### 1.1 核心技术栈
- **Python 3.9+**: 主要开发语言，AI生态丰富
- **FastAPI**: 现代化Web框架，自动生成API文档，高性能异步支持
- **PyTorch**: 深度学习框架，模型部署和推理
- **OpenCV**: 计算机视觉库，图像预处理
- **PaddleOCR**: 百度开源OCR引擎，中文识别效果好
- **PostgreSQL**: 关系型数据库，支持JSON字段
- **Redis**: 缓存和消息队列
- **Docker**: 容器化部署

### 1.2 AI模型选择
- **OCR识别**: PaddleOCR + 自定义后处理
- **图像分类**: ResNet/EfficientNet + 迁移学习
- **目标检测**: YOLOv8 + 自定义数据集训练
- **人脸识别**: FaceNet/ArcFace + 人脸库管理

## 2. 核心模块实现

### 2.1 OCR识别引擎 (ocr_engine.py)
```python
import cv2
import numpy as np
from paddleocr import PaddleOCR
from typing import List, Dict, Tuple
import re

class OCREngine:
    def __init__(self):
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        
    def recognize_vehicle_vin(self, image: np.ndarray) -> Dict:
        """车辆识别代号识别"""
        # 图像预处理
        processed_img = self._preprocess_vin_image(image)
        
        # OCR识别
        results = self.ocr.ocr(processed_img, cls=True)
        
        # 结果后处理
        vin_candidates = []
        for line in results:
            for word_info in line:
                text = word_info[1][0]
                confidence = word_info[1][1]
                
                # VIN码格式验证 (17位字母数字组合)
                if self._validate_vin_format(text):
                    vin_candidates.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': word_info[0]
                    })
        
        # 选择最佳候选
        best_result = max(vin_candidates, key=lambda x: x['confidence']) if vin_candidates else None
        
        return {
            'type': 'vehicle_vin',
            'result': best_result['text'] if best_result else '',
            'confidence': best_result['confidence'] if best_result else 0.0,
            'bbox': best_result['bbox'] if best_result else []
        }
    
    def recognize_engine_number(self, image: np.ndarray) -> Dict:
        """发动机号码识别"""
        processed_img = self._preprocess_engine_image(image)
        results = self.ocr.ocr(processed_img, cls=True)
        
        # 发动机号码通常是字母数字组合，长度6-20位
        engine_candidates = []
        for line in results:
            for word_info in line:
                text = word_info[1][0]
                confidence = word_info[1][1]
                
                if self._validate_engine_format(text):
                    engine_candidates.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': word_info[0]
                    })
        
        best_result = max(engine_candidates, key=lambda x: x['confidence']) if engine_candidates else None
        
        return {
            'type': 'engine_number',
            'result': best_result['text'] if best_result else '',
            'confidence': best_result['confidence'] if best_result else 0.0,
            'bbox': best_result['bbox'] if best_result else []
        }
    
    def recognize_license_plate(self, image: np.ndarray) -> Dict:
        """号牌号码识别"""
        processed_img = self._preprocess_plate_image(image)
        results = self.ocr.ocr(processed_img, cls=True)
        
        plate_candidates = []
        for line in results:
            for word_info in line:
                text = word_info[1][0]
                confidence = word_info[1][1]
                
                # 车牌格式验证
                if self._validate_plate_format(text):
                    plate_candidates.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': word_info[0]
                    })
        
        best_result = max(plate_candidates, key=lambda x: x['confidence']) if plate_candidates else None
        
        return {
            'type': 'license_plate',
            'result': best_result['text'] if best_result else '',
            'confidence': best_result['confidence'] if best_result else 0.0,
            'bbox': best_result['bbox'] if best_result else []
        }
    
    def recognize_tire_specification(self, image: np.ndarray) -> Dict:
        """轮胎规格识别"""
        processed_img = self._preprocess_tire_image(image)
        results = self.ocr.ocr(processed_img, cls=True)
        
        tire_candidates = []
        for line in results:
            for word_info in line:
                text = word_info[1][0]
                confidence = word_info[1][1]
                
                # 轮胎规格格式验证 (如: 225/60R16)
                if self._validate_tire_format(text):
                    tire_candidates.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': word_info[0]
                    })
        
        best_result = max(tire_candidates, key=lambda x: x['confidence']) if tire_candidates else None
        
        return {
            'type': 'tire_specification',
            'result': best_result['text'] if best_result else '',
            'confidence': best_result['confidence'] if best_result else 0.0,
            'bbox': best_result['bbox'] if best_result else []
        }
    
    def _preprocess_vin_image(self, image: np.ndarray) -> np.ndarray:
        """VIN码图像预处理"""
        # 灰度化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        # 去噪
        denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
        return denoised
    
    def _preprocess_engine_image(self, image: np.ndarray) -> np.ndarray:
        """发动机号码图像预处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 自适应阈值
        thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        return thresh
    
    def _preprocess_plate_image(self, image: np.ndarray) -> np.ndarray:
        """车牌图像预处理"""
        # 车牌通常有特定的颜色和形状，可以进行特殊处理
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        processed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        return processed
    
    def _preprocess_tire_image(self, image: np.ndarray) -> np.ndarray:
        """轮胎规格图像预处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(gray, -1, kernel)
        return sharpened
    
    def _validate_vin_format(self, text: str) -> bool:
        """验证VIN码格式"""
        # VIN码17位，不包含I、O、Q
        pattern = r'^[A-HJ-NPR-Z0-9]{17}$'
        return bool(re.match(pattern, text.upper()))
    
    def _validate_engine_format(self, text: str) -> bool:
        """验证发动机号码格式"""
        # 发动机号码通常6-20位字母数字组合
        pattern = r'^[A-Z0-9]{6,20}$'
        return bool(re.match(pattern, text.upper()))
    
    def _validate_plate_format(self, text: str) -> bool:
        """验证车牌格式"""
        # 中国车牌格式验证
        patterns = [
            r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}$',  # 普通车牌
            r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}[A-Z0-9]$',  # 新能源车牌
        ]
        return any(re.match(pattern, text) for pattern in patterns)
    
    def _validate_tire_format(self, text: str) -> bool:
        """验证轮胎规格格式"""
        # 轮胎规格格式如: 225/60R16, 195/65R15
        pattern = r'^\d{3}/\d{2}R\d{2}$'
        return bool(re.match(pattern, text))
```

### 2.2 图像分类引擎 (classification_engine.py)
```python
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
from typing import Dict, List
import json

class ClassificationEngine:
    def __init__(self, model_paths: Dict[str, str]):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载各类分类模型
        self.brand_model = self._load_model(model_paths['brand'])
        self.color_model = self._load_model(model_paths['color'])
        self.type_model = self._load_model(model_paths['type'])
        
        # 加载标签映射
        self.brand_labels = self._load_labels('brand_labels.json')
        self.color_labels = self._load_labels('color_labels.json')
        self.type_labels = self._load_labels('type_labels.json')
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def classify_vehicle_brand(self, image: np.ndarray) -> Dict:
        """车辆品牌标志识别"""
        # 预处理
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        input_tensor = self.transform(pil_image).unsqueeze(0).to(self.device)
        
        # 推理
        with torch.no_grad():
            outputs = self.brand_model(input_tensor)
            probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
            confidence, predicted_idx = torch.max(probabilities, 0)
        
        brand_name = self.brand_labels[predicted_idx.item()]
        
        return {
            'type': 'vehicle_brand',
            'result': brand_name,
            'confidence': confidence.item(),
            'all_predictions': self._get_top_predictions(probabilities, self.brand_labels, top_k=5)
        }
    
    def classify_vehicle_color(self, image: np.ndarray) -> Dict:
        """车身颜色识别"""
        # 颜色识别可能需要特殊的预处理
        processed_image = self._preprocess_color_image(image)
        pil_image = Image.fromarray(cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB))
        input_tensor = self.transform(pil_image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            outputs = self.color_model(input_tensor)
            probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
            confidence, predicted_idx = torch.max(probabilities, 0)
        
        color_name = self.color_labels[predicted_idx.item()]
        
        return {
            'type': 'vehicle_color',
            'result': color_name,
            'confidence': confidence.item(),
            'all_predictions': self._get_top_predictions(probabilities, self.color_labels, top_k=3)
        }
    
    def classify_vehicle_type(self, image: np.ndarray) -> Dict:
        """车辆类型识别"""
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        input_tensor = self.transform(pil_image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            outputs = self.type_model(input_tensor)
            probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
            confidence, predicted_idx = torch.max(probabilities, 0)
        
        vehicle_type = self.type_labels[predicted_idx.item()]
        
        return {
            'type': 'vehicle_type',
            'result': vehicle_type,
            'confidence': confidence.item(),
            'ga_standard': self._map_to_ga_standard(vehicle_type),  # 映射到GA/T 16.4标准
            'all_predictions': self._get_top_predictions(probabilities, self.type_labels, top_k=3)
        }
    
    def _load_model(self, model_path: str):
        """加载预训练模型"""
        model = torch.load(model_path, map_location=self.device)
        model.eval()
        return model
    
    def _load_labels(self, label_file: str) -> List[str]:
        """加载标签映射"""
        with open(label_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _preprocess_color_image(self, image: np.ndarray) -> np.ndarray:
        """颜色识别专用预处理"""
        # 可能需要特殊的颜色空间转换或区域提取
        # 例如：提取车身主要区域，排除车窗、轮胎等
        return image
    
    def _get_top_predictions(self, probabilities: torch.Tensor, labels: List[str], top_k: int = 5) -> List[Dict]:
        """获取Top-K预测结果"""
        top_probs, top_indices = torch.topk(probabilities, top_k)
        return [
            {'label': labels[idx.item()], 'confidence': prob.item()}
            for prob, idx in zip(top_probs, top_indices)
        ]
    
    def _map_to_ga_standard(self, vehicle_type: str) -> str:
        """映射到GA/T 16.4标准"""
        # 根据GA/T 16.4标准进行映射
        ga_mapping = {
            '小型轿车': 'K11',
            '中型轿车': 'K21', 
            '大型轿车': 'K31',
            # ... 更多映射关系
        }
        return ga_mapping.get(vehicle_type, 'Unknown')
```

## 3. 下一步实现计划

### 3.1 立即开始的任务
1. 创建项目基础结构
2. 实现OCR识别引擎
3. 实现图像分类引擎
4. 实现目标检测引擎
5. 创建FastAPI应用框架

### 3.2 数据准备
1. 收集各类识别任务的训练数据
2. 标注数据集
3. 训练和优化AI模型
4. 建立测试数据集

### 3.3 系统集成
1. 数据库设计和实现
2. API接口开发
3. 前端界面开发
4. 系统测试和优化

这个实现方案提供了详细的技术路线和核心代码框架，可以作为项目开发的具体指导。
