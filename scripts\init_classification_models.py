#!/usr/bin/env python3
"""
初始化分类模型脚本
用于创建示例模型文件和标签文件，便于测试
"""
import os
import json
import torch
import torch.nn as nn
from torchvision import models
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_model_directories():
    """创建模型目录"""
    directories = [
        "ai_models",
        "ai_models/classification",
        "ai_models/ocr",
        "ai_models/detection",
        "ai_models/face_recognition"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")


def create_brand_model():
    """创建品牌分类模型"""
    try:
        # 品牌列表
        brand_names = [
            "奥迪", "宝马", "奔驰", "大众", "丰田", "本田", "日产", "现代",
            "起亚", "福特", "雪佛兰", "别克", "凯迪拉克", "沃尔沃", "捷豹",
            "路虎", "保时捷", "法拉利", "兰博基尼", "玛莎拉蒂", "阿斯顿马丁",
            "宾利", "劳斯莱斯", "迈凯伦", "布加迪", "比亚迪", "吉利", "长城",
            "奇瑞", "长安", "一汽", "东风", "上汽", "广汽", "北汽", "江淮",
            "江铃", "五菱", "宝骏", "荣威", "名爵", "传祺", "启辰", "观致",
            "众泰", "海马", "力帆", "华泰", "中华", "红旗", "解放", "东风商用车"
        ]
        
        num_classes = len(brand_names)
        
        # 创建模型
        model = models.resnet50(pretrained=True)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
        
        # 保存模型
        model_path = "ai_models/classification/vehicle_brand.pth"
        torch.save({
            'model_state_dict': model.state_dict(),
            'num_classes': num_classes,
            'model_type': 'ResNet50'
        }, model_path)
        
        # 保存标签
        labels_path = "ai_models/classification/brand_labels.json"
        labels_data = {
            'class_names': brand_names,
            'num_classes': num_classes,
            'description': '车辆品牌分类标签'
        }
        
        with open(labels_path, 'w', encoding='utf-8') as f:
            json.dump(labels_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"品牌分类模型创建成功: {model_path}")
        logger.info(f"品牌标签文件创建成功: {labels_path}")
        
    except Exception as e:
        logger.error(f"创建品牌分类模型失败: {str(e)}")


def create_color_model():
    """创建颜色分类模型"""
    try:
        color_names = [
            "白色", "黑色", "银色", "灰色", "红色", "蓝色", "绿色", "黄色",
            "棕色", "橙色", "紫色", "粉色", "金色", "米色", "深蓝色", "深绿色"
        ]
        
        num_classes = len(color_names)
        
        # 创建模型
        model = models.resnet34(pretrained=True)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
        
        # 保存模型
        model_path = "ai_models/classification/vehicle_color.pth"
        torch.save({
            'model_state_dict': model.state_dict(),
            'num_classes': num_classes,
            'model_type': 'ResNet34',
            'class_names': color_names
        }, model_path)
        
        logger.info(f"颜色分类模型创建成功: {model_path}")
        
    except Exception as e:
        logger.error(f"创建颜色分类模型失败: {str(e)}")


def create_type_model():
    """创建车辆类型分类模型"""
    try:
        # GA/T 16.4 车辆类型标准
        vehicle_types = {
            'K11': '小型载客汽车',
            'K12': '中型载客汽车', 
            'K13': '大型载客汽车',
            'K21': '小型载货汽车',
            'K22': '中型载货汽车',
            'K23': '重型载货汽车',
            'K31': '小型专项作业车',
            'K32': '中型专项作业车',
            'K33': '重型专项作业车',
            'K41': '轻型牵引车',
            'K42': '中型牵引车',
            'K43': '重型牵引车',
            'M1': '乘用车',
            'M2': '轻型客车',
            'M3': '大型客车',
            'N1': '轻型货车',
            'N2': '中型货车',
            'N3': '重型货车'
        }
        
        type_codes = list(vehicle_types.keys())
        num_classes = len(type_codes)
        
        # 创建模型
        model = models.resnet101(pretrained=True)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
        
        # 保存模型
        model_path = "ai_models/classification/vehicle_type.pth"
        torch.save({
            'model_state_dict': model.state_dict(),
            'num_classes': num_classes,
            'model_type': 'ResNet101',
            'vehicle_types': vehicle_types,
            'type_codes': type_codes,
            'standard': 'GA/T 16.4'
        }, model_path)
        
        logger.info(f"车辆类型分类模型创建成功: {model_path}")
        
    except Exception as e:
        logger.error(f"创建车辆类型分类模型失败: {str(e)}")


def create_readme():
    """创建模型说明文件"""
    readme_content = """# AI模型文件说明

## 分类模型

### 1. 车辆品牌分类 (vehicle_brand.pth)
- 模型架构: ResNet50
- 支持品牌: 50+个主流汽车品牌
- 输入尺寸: 224x224x3
- 输出: 品牌分类概率

### 2. 车身颜色分类 (vehicle_color.pth)
- 模型架构: ResNet34
- 支持颜色: 16种常见车身颜色
- 输入尺寸: 224x224x3
- 输出: 颜色分类概率

### 3. 车辆类型分类 (vehicle_type.pth)
- 模型架构: ResNet101
- 分类标准: GA/T 16.4
- 支持类型: 18种车辆类型
- 输入尺寸: 224x224x3
- 输出: 类型分类概率

## 使用说明

1. 这些是示例模型文件，用于系统测试
2. 实际部署时需要使用真实训练的模型
3. 模型文件包含预训练权重，可直接加载使用
4. 标签文件包含类别名称和相关信息

## 模型训练

如需训练自定义模型，请参考以下步骤：
1. 准备标注数据集
2. 使用PyTorch进行模型训练
3. 保存训练好的权重文件
4. 更新标签配置文件

## 注意事项

- 模型文件较大，请确保有足够的存储空间
- GPU环境下可获得更好的推理性能
- 定期更新模型以提高识别准确率
"""
    
    with open("ai_models/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    logger.info("模型说明文件创建成功: ai_models/README.md")


def main():
    """主函数"""
    logger.info("开始初始化分类模型...")
    
    # 创建目录
    create_model_directories()
    
    # 创建模型文件
    create_brand_model()
    create_color_model()
    create_type_model()
    
    # 创建说明文件
    create_readme()
    
    logger.info("分类模型初始化完成！")
    logger.info("注意: 这些是示例模型，实际使用时需要替换为真实训练的模型")


if __name__ == "__main__":
    main()
