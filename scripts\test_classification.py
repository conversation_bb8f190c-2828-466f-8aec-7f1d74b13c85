#!/usr/bin/env python3
"""
分类引擎测试脚本
"""
import sys
import os
import asyncio
import base64
import numpy as np
import cv2
from PIL import Image
import io
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.services.classification_service import classification_service
from app.config.settings import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_image(color_rgb=(255, 0, 0), size=(224, 224)):
    """
    创建测试图像
    
    Args:
        color_rgb: RGB颜色值
        size: 图像尺寸
        
    Returns:
        base64编码的图像数据
    """
    # 创建纯色图像
    image = np.full((*size, 3), color_rgb, dtype=np.uint8)
    
    # 添加一些纹理使图像更真实
    noise = np.random.randint(-20, 20, (*size, 3))
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # 转换为PIL图像
    pil_image = Image.fromarray(image)
    
    # 转换为base64
    buffer = io.BytesIO()
    pil_image.save(buffer, format='JPEG', quality=95)
    image_bytes = buffer.getvalue()
    
    return base64.b64encode(image_bytes).decode()


def create_vehicle_test_image():
    """创建模拟车辆图像"""
    # 创建一个简单的车辆轮廓图像
    image = np.ones((224, 224, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 绘制简单的车辆形状
    cv2.rectangle(image, (50, 100), (174, 150), (0, 0, 255), -1)  # 红色车身
    cv2.rectangle(image, (60, 80), (164, 100), (100, 100, 100), -1)  # 车顶
    cv2.circle(image, (70, 160), 15, (0, 0, 0), -1)  # 左轮
    cv2.circle(image, (154, 160), 15, (0, 0, 0), -1)  # 右轮
    
    # 转换为base64
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    buffer = io.BytesIO()
    pil_image.save(buffer, format='JPEG', quality=95)
    image_bytes = buffer.getvalue()
    
    return base64.b64encode(image_bytes).decode()


async def test_brand_classification():
    """测试品牌分类"""
    logger.info("=== 测试品牌分类 ===")
    
    try:
        # 创建测试图像
        test_image = create_vehicle_test_image()
        
        # 执行分类
        result = await classification_service.classify_vehicle_brand(test_image, top_k=5)
        
        if result['success']:
            logger.info("品牌分类成功!")
            if result['best_match']:
                logger.info(f"最佳匹配: {result['best_match']['brand']} (置信度: {result['best_match']['confidence']:.3f})")
            
            logger.info("前5个候选:")
            for i, candidate in enumerate(result['results'][:5], 1):
                logger.info(f"  {i}. {candidate['brand']} - {candidate['confidence']:.3f}")
            
            logger.info(f"处理时间: {result['processing_time']:.3f}秒")
        else:
            logger.error(f"品牌分类失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"品牌分类测试异常: {str(e)}")


async def test_color_classification():
    """测试颜色分类"""
    logger.info("=== 测试颜色分类 ===")
    
    try:
        # 创建红色测试图像
        test_image = create_test_image(color_rgb=(255, 0, 0))
        
        # 执行分类
        result = await classification_service.classify_vehicle_color(test_image)
        
        if result['success']:
            logger.info("颜色分类成功!")
            if result['best_match']:
                logger.info(f"最佳匹配: {result['best_match']['color']} (置信度: {result['best_match']['confidence']:.3f})")
            
            logger.info("前3个候选:")
            for i, candidate in enumerate(result['results'][:3], 1):
                logger.info(f"  {i}. {candidate['color']} - {candidate['confidence']:.3f}")
            
            logger.info(f"处理时间: {result['processing_time']:.3f}秒")
        else:
            logger.error(f"颜色分类失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"颜色分类测试异常: {str(e)}")


async def test_type_classification():
    """测试类型分类"""
    logger.info("=== 测试类型分类 ===")
    
    try:
        # 创建测试图像
        test_image = create_vehicle_test_image()
        
        # 执行分类
        result = await classification_service.classify_vehicle_type(test_image)
        
        if result['success']:
            logger.info("类型分类成功!")
            if result['best_match']:
                logger.info(f"最佳匹配: {result['best_match']['type_code']} - {result['best_match']['type_name']}")
                logger.info(f"置信度: {result['best_match']['confidence']:.3f}")
            
            logger.info("前3个候选:")
            for i, candidate in enumerate(result['results'][:3], 1):
                logger.info(f"  {i}. {candidate['type_code']} - {candidate['type_name']} ({candidate['confidence']:.3f})")
            
            logger.info(f"处理时间: {result['processing_time']:.3f}秒")
            logger.info(f"分类标准: {result.get('standard', 'N/A')}")
        else:
            logger.error(f"类型分类失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"类型分类测试异常: {str(e)}")


async def test_batch_classification():
    """测试批量分类"""
    logger.info("=== 测试批量分类 ===")
    
    try:
        # 准备批量请求
        requests = [
            {
                'image_data': create_test_image(color_rgb=(255, 0, 0)),  # 红色
                'type': 'vehicle_color',
                'kwargs': {}
            },
            {
                'image_data': create_test_image(color_rgb=(0, 255, 0)),  # 绿色
                'type': 'vehicle_color',
                'kwargs': {}
            },
            {
                'image_data': create_vehicle_test_image(),
                'type': 'vehicle_brand',
                'kwargs': {'top_k': 3}
            }
        ]
        
        # 执行批量分类
        results = await classification_service.batch_classify(requests)
        
        logger.info(f"批量分类完成，处理了 {len(results)} 个请求")
        
        for i, result in enumerate(results):
            logger.info(f"请求 {i+1}:")
            if result['success']:
                if result['best_match']:
                    if result['type'] == 'vehicle_color':
                        logger.info(f"  颜色: {result['best_match']['color']} ({result['best_match']['confidence']:.3f})")
                    elif result['type'] == 'vehicle_brand':
                        logger.info(f"  品牌: {result['best_match']['brand']} ({result['best_match']['confidence']:.3f})")
                else:
                    logger.info(f"  无匹配结果 (置信度过低)")
            else:
                logger.error(f"  失败: {result['error']}")
                
    except Exception as e:
        logger.error(f"批量分类测试异常: {str(e)}")


async def test_service_status():
    """测试服务状态"""
    logger.info("=== 测试服务状态 ===")
    
    try:
        status = classification_service.get_service_status()
        
        logger.info(f"服务名称: {status['service_name']}")
        logger.info(f"初始化状态: {status['is_initialized']}")
        
        logger.info("置信度阈值:")
        for key, value in status['confidence_thresholds'].items():
            logger.info(f"  {key}: {value}")
        
        logger.info("引擎状态:")
        engines_status = status['engines_status']
        for engine_name, engine_info in engines_status.items():
            logger.info(f"  {engine_name}: {'已加载' if engine_info['loaded'] else '未加载'}")
            
    except Exception as e:
        logger.error(f"服务状态测试异常: {str(e)}")


async def main():
    """主测试函数"""
    logger.info("开始分类引擎测试...")
    
    # 初始化服务
    logger.info("初始化分类服务...")
    device = "cuda" if os.environ.get("USE_GPU", "false").lower() == "true" else "cpu"
    success = await classification_service.initialize_service(device)
    
    if not success:
        logger.warning("分类服务初始化失败，但继续测试...")
    
    # 测试服务状态
    await test_service_status()
    
    # 如果服务初始化成功，进行功能测试
    if success:
        await test_brand_classification()
        await test_color_classification()
        await test_type_classification()
        await test_batch_classification()
    else:
        logger.info("跳过功能测试，因为服务未成功初始化")
        logger.info("请先运行 scripts/init_classification_models.py 创建模型文件")
    
    logger.info("分类引擎测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
