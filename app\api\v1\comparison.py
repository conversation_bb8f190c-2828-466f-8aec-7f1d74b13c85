"""
比对服务API路由
"""
from fastapi import APIRouter, HTTPException
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/compare")
async def compare_results():
    """识别结果比对"""
    # TODO: 实现比对逻辑
    return {"message": "比对服务待实现"}


@router.get("/history/{task_id}")
async def get_comparison_history(task_id: str):
    """获取比对历史"""
    # TODO: 实现比对历史查询
    return {"message": "比对历史查询待实现"}
