"""
AI引擎基类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
import logging
import time
import numpy as np
import cv2
from PIL import Image
import torch

from app.core.utils.exceptions import AIEngineException

logger = logging.getLogger(__name__)


class BaseAIEngine(ABC):
    """AI引擎基类"""
    
    def __init__(self, model_path: str, device: str = "cpu"):
        """
        初始化AI引擎
        
        Args:
            model_path: 模型文件路径
            device: 计算设备 (cpu/cuda)
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self.is_loaded = False
        self.load_time = None
        
    @abstractmethod
    def load_model(self) -> bool:
        """加载模型"""
        pass
    
    @abstractmethod
    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """预测方法"""
        pass
    
    def preprocess_image(self, image: np.ndarray, target_size: Tuple[int, int] = None) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            
        Returns:
            预处理后的图像
        """
        try:
            # 确保图像是RGB格式
            if len(image.shape) == 3 and image.shape[2] == 3:
                # BGR to RGB
                if image.dtype == np.uint8:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 调整图像尺寸
            if target_size:
                image = cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)
            
            return image
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            raise AIEngineException(f"图像预处理失败: {str(e)}", 2001)
    
    def postprocess_results(self, raw_results: Any, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        结果后处理
        
        Args:
            raw_results: 原始预测结果
            confidence_threshold: 置信度阈值
            
        Returns:
            处理后的结果列表
        """
        # 子类可以重写此方法
        return raw_results
    
    def validate_image(self, image: np.ndarray) -> bool:
        """
        验证图像有效性
        
        Args:
            image: 输入图像
            
        Returns:
            是否有效
        """
        if image is None:
            return False
        
        if not isinstance(image, np.ndarray):
            return False
        
        if len(image.shape) not in [2, 3]:
            return False
        
        if image.size == 0:
            return False
        
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "load_time": self.load_time
        }
    
    def warmup(self, warmup_size: Tuple[int, int] = (224, 224)) -> bool:
        """
        模型预热
        
        Args:
            warmup_size: 预热图像尺寸
            
        Returns:
            预热是否成功
        """
        try:
            if not self.is_loaded:
                logger.warning("模型未加载，无法进行预热")
                return False
            
            # 创建随机图像进行预热
            dummy_image = np.random.randint(0, 255, (*warmup_size, 3), dtype=np.uint8)
            
            # 执行一次预测
            start_time = time.time()
            _ = self.predict(dummy_image)
            warmup_time = time.time() - start_time
            
            logger.info(f"模型预热完成，耗时: {warmup_time:.3f}秒")
            return True
            
        except Exception as e:
            logger.error(f"模型预热失败: {str(e)}")
            return False


class TorchBaseEngine(BaseAIEngine):
    """基于PyTorch的AI引擎基类"""
    
    def __init__(self, model_path: str, device: str = "cpu"):
        super().__init__(model_path, device)
        
        # 设置PyTorch设备
        if device == "cuda" and torch.cuda.is_available():
            self.torch_device = torch.device("cuda")
            logger.info(f"使用GPU设备: {torch.cuda.get_device_name()}")
        else:
            self.torch_device = torch.device("cpu")
            logger.info("使用CPU设备")
    
    def load_model(self) -> bool:
        """加载PyTorch模型"""
        try:
            start_time = time.time()
            
            # 加载模型
            self.model = torch.load(self.model_path, map_location=self.torch_device)
            self.model.eval()
            
            self.load_time = time.time() - start_time
            self.is_loaded = True
            
            logger.info(f"PyTorch模型加载成功，耗时: {self.load_time:.3f}秒")
            return True
            
        except Exception as e:
            logger.error(f"PyTorch模型加载失败: {str(e)}")
            raise AIEngineException(f"模型加载失败: {str(e)}", 2001)
    
    def tensor_to_numpy(self, tensor: torch.Tensor) -> np.ndarray:
        """将PyTorch张量转换为NumPy数组"""
        return tensor.detach().cpu().numpy()
    
    def numpy_to_tensor(self, array: np.ndarray) -> torch.Tensor:
        """将NumPy数组转换为PyTorch张量"""
        tensor = torch.from_numpy(array).float()
        return tensor.to(self.torch_device)


class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.engines = {}
        self.load_status = {}
    
    def register_engine(self, name: str, engine: BaseAIEngine) -> bool:
        """
        注册AI引擎
        
        Args:
            name: 引擎名称
            engine: AI引擎实例
            
        Returns:
            注册是否成功
        """
        try:
            self.engines[name] = engine
            self.load_status[name] = False
            logger.info(f"AI引擎 '{name}' 注册成功")
            return True
        except Exception as e:
            logger.error(f"AI引擎 '{name}' 注册失败: {str(e)}")
            return False
    
    def load_engine(self, name: str) -> bool:
        """
        加载指定的AI引擎
        
        Args:
            name: 引擎名称
            
        Returns:
            加载是否成功
        """
        if name not in self.engines:
            logger.error(f"AI引擎 '{name}' 未注册")
            return False
        
        try:
            engine = self.engines[name]
            success = engine.load_model()
            self.load_status[name] = success
            
            if success:
                # 执行预热
                engine.warmup()
            
            return success
        except Exception as e:
            logger.error(f"AI引擎 '{name}' 加载失败: {str(e)}")
            self.load_status[name] = False
            return False
    
    def get_engine(self, name: str) -> Optional[BaseAIEngine]:
        """获取AI引擎实例"""
        if name in self.engines and self.load_status.get(name, False):
            return self.engines[name]
        return None
    
    def get_all_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有引擎状态"""
        status = {}
        for name, engine in self.engines.items():
            status[name] = {
                "is_loaded": self.load_status.get(name, False),
                "model_info": engine.get_model_info()
            }
        return status
    
    def unload_engine(self, name: str) -> bool:
        """卸载指定的AI引擎"""
        if name in self.engines:
            try:
                engine = self.engines[name]
                if hasattr(engine, 'model') and engine.model is not None:
                    del engine.model
                    engine.model = None
                    engine.is_loaded = False
                    self.load_status[name] = False
                
                logger.info(f"AI引擎 '{name}' 卸载成功")
                return True
            except Exception as e:
                logger.error(f"AI引擎 '{name}' 卸载失败: {str(e)}")
                return False
        return False


# 全局模型管理器实例
model_manager = ModelManager()
