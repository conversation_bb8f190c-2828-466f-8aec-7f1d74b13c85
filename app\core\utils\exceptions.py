"""
自定义异常类
"""
from typing import Optional


class AutoInspectException(Exception):
    """自定义业务异常基类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[int] = None,
        status_code: int = 400
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


class ImageProcessingException(AutoInspectException):
    """图像处理异常"""
    
    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message, error_code, 400)


class AIEngineException(AutoInspectException):
    """AI引擎异常"""
    
    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message, error_code, 500)


class DatabaseException(AutoInspectException):
    """数据库异常"""
    
    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message, error_code, 500)


class ExternalAPIException(AutoInspectException):
    """外部API异常"""
    
    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message, error_code, 502)
