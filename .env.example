# 环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 应用配置
APP_NAME=AutoInspectAI
APP_VERSION=1.0.0
DEBUG=True
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 数据库配置
DATABASE_URL=postgresql://autoinspect:password@localhost:5432/autoinspect_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# 文件存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=autoinspect-images
MINIO_SECURE=False

# AI模型配置
AI_MODELS_PATH=./ai_models
OCR_MODEL_PATH=./ai_models/ocr/
CLASSIFICATION_MODEL_PATH=./ai_models/classification/
DETECTION_MODEL_PATH=./ai_models/detection/
FACE_MODEL_PATH=./ai_models/face_recognition/

# 识别阈值配置
OCR_CONFIDENCE_THRESHOLD=0.8
CLASSIFICATION_CONFIDENCE_THRESHOLD=0.7
DETECTION_CONFIDENCE_THRESHOLD=0.6
FACE_CONFIDENCE_THRESHOLD=0.8

# 性能配置
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=30
MAX_IMAGE_SIZE=10485760  # 10MB
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,bmp

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=Asia/Shanghai

# 外部接口配置
POLICE_PLATFORM_URL=https://api.police.gov.cn
POLICE_PLATFORM_TOKEN=your_token_here
POLICE_PLATFORM_TIMEOUT=30

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# 监控配置
PROMETHEUS_ENABLED=True
PROMETHEUS_PORT=8001

# 日志配置
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
