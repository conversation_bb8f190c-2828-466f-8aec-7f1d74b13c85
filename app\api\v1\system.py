"""
系统管理API路由
"""
from fastapi import APIRouter, HTTPException
import logging
import psutil
import time

from app.config.settings import settings

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 获取系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "system": {
                "cpu_usage": f"{cpu_percent}%",
                "memory_usage": f"{memory.percent}%",
                "memory_available": f"{memory.available / 1024 / 1024 / 1024:.2f}GB",
                "disk_usage": f"{disk.percent}%",
                "disk_free": f"{disk.free / 1024 / 1024 / 1024:.2f}GB"
            },
            "application": {
                "name": settings.APP_NAME,
                "version": settings.APP_VERSION,
                "debug": settings.DEBUG,
                "uptime": time.time()  # 简化的运行时间
            },
            "ai_engines": {
                "ocr_status": "ready",  # TODO: 实际检查AI引擎状态
                "classification_status": "ready",
                "detection_status": "ready",
                "face_recognition_status": "ready"
            }
        }
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/config")
async def get_system_config():
    """获取系统配置信息"""
    return {
        "recognition_types": len(settings.RECOGNITION_TYPES) if hasattr(settings, 'RECOGNITION_TYPES') else 0,
        "max_concurrent_tasks": settings.MAX_CONCURRENT_TASKS,
        "task_timeout": settings.TASK_TIMEOUT,
        "max_image_size": settings.MAX_IMAGE_SIZE,
        "supported_formats": settings.supported_image_formats_list
    }


@router.post("/reload_models")
async def reload_ai_models():
    """重新加载AI模型"""
    try:
        # TODO: 实现模型重新加载逻辑
        logger.info("开始重新加载AI模型")
        
        # 这里应该调用AI引擎的重新加载方法
        
        return {"message": "AI模型重新加载成功"}
    except Exception as e:
        logger.error(f"重新加载AI模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新加载模型失败: {str(e)}")


@router.get("/logs")
async def get_system_logs(lines: int = 100):
    """获取系统日志"""
    try:
        # TODO: 实现日志读取逻辑
        return {
            "logs": [
                f"示例日志行 {i}" for i in range(lines)
            ]
        }
    except Exception as e:
        logger.error(f"获取系统日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")
