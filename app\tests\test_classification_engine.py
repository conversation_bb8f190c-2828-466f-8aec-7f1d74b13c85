"""
分类引擎测试
"""
import pytest
import numpy as np
import cv2
import base64
import io
from PIL import Image
import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from app.core.ai_engines.classification_engine import (
    VehicleBrandClassifier,
    VehicleColorClassifier, 
    VehicleTypeClassifier,
    classification_manager
)
from app.core.services.classification_service import classification_service


class TestClassificationEngine:
    """分类引擎测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试用的样本图像"""
        # 创建一个简单的彩色图像
        image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        return image
    
    @pytest.fixture
    def sample_image_base64(self, sample_image):
        """创建base64编码的测试图像"""
        # 将numpy数组转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB))
        
        # 转换为base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG')
        image_bytes = buffer.getvalue()
        
        return base64.b64encode(image_bytes).decode()
    
    def test_vehicle_brand_classifier_init(self):
        """测试品牌分类器初始化"""
        model_path = "test_brand_model.pth"
        labels_path = "test_brand_labels.json"
        
        classifier = VehicleBrandClassifier(model_path, labels_path, "cpu")
        
        assert classifier.model_path == model_path
        assert classifier.labels_path == labels_path
        assert classifier.device == "cpu"
        assert not classifier.is_loaded
    
    def test_vehicle_color_classifier_init(self):
        """测试颜色分类器初始化"""
        model_path = "test_color_model.pth"
        
        classifier = VehicleColorClassifier(model_path, "cpu")
        
        assert classifier.model_path == model_path
        assert classifier.device == "cpu"
        assert not classifier.is_loaded
        assert len(classifier.color_names) > 0
    
    def test_vehicle_type_classifier_init(self):
        """测试类型分类器初始化"""
        model_path = "test_type_model.pth"
        
        classifier = VehicleTypeClassifier(model_path, "cpu")
        
        assert classifier.model_path == model_path
        assert classifier.device == "cpu"
        assert not classifier.is_loaded
        assert len(classifier.vehicle_types) > 0
        assert 'K11' in classifier.vehicle_types  # 检查GA/T 16.4标准
    
    def test_image_validation(self, sample_image):
        """测试图像验证功能"""
        classifier = VehicleBrandClassifier("test.pth", "test.json", "cpu")
        
        # 测试有效图像
        assert classifier.validate_image(sample_image) == True
        
        # 测试无效图像
        assert classifier.validate_image(None) == False
        assert classifier.validate_image(np.array([])) == False
        assert classifier.validate_image("not_an_array") == False
    
    def test_image_preprocessing(self, sample_image):
        """测试图像预处理"""
        classifier = VehicleBrandClassifier("test.pth", "test.json", "cpu")
        
        # 测试预处理
        processed = classifier.preprocess_image(sample_image, target_size=(224, 224))
        
        assert processed is not None
        assert processed.shape == (224, 224, 3)
    
    @pytest.mark.asyncio
    async def test_classification_service_init(self):
        """测试分类服务初始化"""
        # 注意：这个测试可能会失败，因为模型文件不存在
        # 在实际环境中需要准备真实的模型文件
        try:
            success = await classification_service.initialize_service("cpu")
            # 即使模型文件不存在，服务也应该能够初始化（只是模型加载失败）
            assert isinstance(success, bool)
        except Exception as e:
            # 预期的异常，因为模型文件不存在
            assert "模型" in str(e) or "文件" in str(e)
    
    def test_image_decode(self, sample_image_base64):
        """测试图像解码"""
        try:
            decoded_image = classification_service.decode_image(sample_image_base64)
            assert decoded_image is not None
            assert isinstance(decoded_image, np.ndarray)
            assert len(decoded_image.shape) == 3  # 彩色图像
        except Exception as e:
            # 如果解码失败，检查错误信息
            assert "解码" in str(e)
    
    def test_image_quality_validation(self, sample_image):
        """测试图像质量验证"""
        quality_result = classification_service.validate_image_quality(sample_image)
        
        assert 'valid' in quality_result
        assert isinstance(quality_result['valid'], bool)
        
        if quality_result['valid']:
            assert 'size' in quality_result
            assert 'blur_score' in quality_result
            assert 'brightness' in quality_result
        else:
            assert 'reason' in quality_result
    
    def test_confidence_thresholds(self):
        """测试置信度阈值设置"""
        thresholds = classification_service.confidence_thresholds
        
        assert 'vehicle_brand' in thresholds
        assert 'vehicle_color' in thresholds
        assert 'vehicle_type' in thresholds
        
        for threshold in thresholds.values():
            assert 0.0 <= threshold <= 1.0
    
    def test_service_status(self):
        """测试服务状态获取"""
        status = classification_service.get_service_status()
        
        assert 'service_name' in status
        assert 'is_initialized' in status
        assert 'confidence_thresholds' in status
        assert 'engines_status' in status
        
        assert status['service_name'] == 'ClassificationService'
    
    @pytest.mark.asyncio
    async def test_classify_vehicle_brand_error_handling(self):
        """测试品牌分类错误处理"""
        # 测试无效的base64数据
        invalid_base64 = "invalid_base64_data"
        
        result = await classification_service.classify_vehicle_brand(invalid_base64)
        
        assert 'success' in result
        assert result['success'] == False
        assert 'error' in result
    
    def test_ga_t_16_4_standard(self):
        """测试GA/T 16.4标准符合性"""
        classifier = VehicleTypeClassifier("test.pth", "cpu")
        
        # 检查是否包含标准的车辆类型代码
        expected_codes = ['K11', 'K12', 'K13', 'K21', 'K22', 'K23', 'M1', 'M2', 'M3', 'N1', 'N2', 'N3']
        
        for code in expected_codes:
            assert code in classifier.vehicle_types
            assert isinstance(classifier.vehicle_types[code], str)
            assert len(classifier.vehicle_types[code]) > 0


class TestClassificationManager:
    """分类管理器测试类"""
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        manager = classification_manager
        
        # 检查管理器属性
        assert hasattr(manager, 'brand_classifier')
        assert hasattr(manager, 'color_classifier')
        assert hasattr(manager, 'type_classifier')
        assert hasattr(manager, 'is_initialized')
    
    def test_manager_status(self):
        """测试管理器状态"""
        status = classification_manager.get_status()
        
        assert 'brand_classifier' in status
        assert 'color_classifier' in status
        assert 'type_classifier' in status
        assert 'is_initialized' in status
        
        for classifier_status in ['brand_classifier', 'color_classifier', 'type_classifier']:
            assert 'loaded' in status[classifier_status]
            assert isinstance(status[classifier_status]['loaded'], bool)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
