"""
FastAPI应用主入口
"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import logging.config
import time
import uuid

from app.config.settings import settings, LOGGING_CONFIG
from app.api.v1 import recognition, comparison, data, system
from app.core.utils.exceptions import AutoInspectException
from app.models.database import init_db
from app.core.services.classification_service import classification_service


# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info(f"启动 {settings.APP_NAME} v{settings.APP_VERSION}")
    
    # 初始化数据库
    await init_db()
    logger.info("数据库初始化完成")

    # 初始化分类服务
    try:
        device = "cuda" if settings.USE_GPU else "cpu"
        success = await classification_service.initialize_service(device)
        if success:
            logger.info(f"分类服务初始化成功 (设备: {device})")
        else:
            logger.warning("分类服务初始化失败，部分功能可能不可用")
    except Exception as e:
        logger.error(f"分类服务初始化异常: {str(e)}")

    logger.info("AI引擎初始化完成")
    
    yield
    
    # 关闭时执行
    logger.info("应用正在关闭...")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="车辆智能识别系统 - 基于深度学习的车辆信息智能识别平台",
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["https://yourdomain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 添加可信主机中间件
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["yourdomain.com", "*.yourdomain.com"]
    )


# 请求ID中间件
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    """为每个请求添加唯一ID"""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    # 添加到响应头
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    
    return response


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求开始
    logger.info(
        f"请求开始 - {request.method} {request.url.path} "
        f"- Request ID: {getattr(request.state, 'request_id', 'unknown')}"
    )
    
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录请求结束
    logger.info(
        f"请求结束 - {request.method} {request.url.path} "
        f"- Status: {response.status_code} "
        f"- Time: {process_time:.3f}s "
        f"- Request ID: {getattr(request.state, 'request_id', 'unknown')}"
    )
    
    # 添加处理时间到响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 全局异常处理器
@app.exception_handler(AutoInspectException)
async def autoinspect_exception_handler(request: Request, exc: AutoInspectException):
    """处理自定义异常"""
    logger.error(
        f"业务异常 - {exc.error_code}: {exc.message} "
        f"- Request ID: {getattr(request.state, 'request_id', 'unknown')}"
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "error_code": exc.error_code,
            "message": exc.message,
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    logger.error(
        f"HTTP异常 - {exc.status_code}: {exc.detail} "
        f"- Request ID: {getattr(request.state, 'request_id', 'unknown')}"
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理通用异常"""
    logger.error(
        f"系统异常 - {type(exc).__name__}: {str(exc)} "
        f"- Request ID: {getattr(request.state, 'request_id', 'unknown')}",
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "系统内部错误" if not settings.DEBUG else str(exc),
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


# 健康检查端点
@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "timestamp": time.time()
    }


@app.get("/", tags=["系统"])
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs_url": "/docs" if settings.DEBUG else None
    }


# 注册API路由
app.include_router(
    recognition.router,
    prefix="/api/v1/recognition",
    tags=["识别服务"]
)

app.include_router(
    comparison.router,
    prefix="/api/v1/comparison",
    tags=["比对服务"]
)

app.include_router(
    data.router,
    prefix="/api/v1/data",
    tags=["数据服务"]
)

app.include_router(
    system.router,
    prefix="/api/v1/system",
    tags=["系统管理"]
)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.WORKERS,
        log_config=LOGGING_CONFIG
    )
