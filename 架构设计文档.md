# 车辆智能识别系统架构设计文档

## 1. 系统概述

### 1.1 项目背景
车辆智能识别系统是一个基于计算机视觉和深度学习技术的综合性识别平台，主要用于机动车查验过程中的自动化识别和比对。系统需要实现多种车辆相关信息的智能识别，包括车辆识别代号、发动机号码、品牌标志、车身颜色等多个维度的识别功能。

### 1.2 系统目标
- 实现9大类车辆信息的智能识别，识别准确率≥80%-90%
- 支持多种照片类型的识别处理
- 提供与公安交通管理综合应用平台的数据交互能力
- 构建可扩展、高性能的识别服务架构

## 2. 功能需求分析

### 2.1 核心识别功能
1. **车辆识别代号识别** (准确率≥90%)
2. **发动机(驱动电机)号码识别** (准确率≥90%)
3. **车辆品牌标志识别** (准确率≥90%, 支持≥100种品牌)
4. **车身颜色识别** (准确率≥80%)
5. **车辆类型识别比对** (准确率≥80%, 符合GA/T 16.4规定)
6. **轮胎规格识别** (准确率≥90%)
7. **三角警告牌、反光背心识别** (准确率≥90%)
8. **充电接口识别** (准确率≥90%)
9. **号牌号码识别** (准确率≥90%)
10. **查验员识别** (准确率≥90%)

### 2.2 数据交互功能
- 待比对信息下载
- 识别结果信息比对和上传

## 3. 系统架构设计

### 3.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────────────────────────────────────────────────┤
│                    API网关层                                │
├─────────────────────────────────────────────────────────────┤
│                    业务服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  识别服务    │ │  比对服务    │ │  数据服务    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    AI引擎层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  OCR引擎     │ │  图像分类    │ │  目标检测    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  关系数据库  │ │  文件存储    │ │  缓存系统    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 技术栈选择
- **后端框架**: FastAPI (Python)
- **AI框架**: PyTorch, OpenCV, PaddleOCR
- **数据库**: PostgreSQL (主数据库), Redis (缓存)
- **文件存储**: MinIO 或 阿里云OSS
- **消息队列**: RabbitMQ 或 Redis
- **容器化**: Docker + Docker Compose
- **API文档**: Swagger/OpenAPI

## 4. 详细模块设计

### 4.1 AI引擎层设计

#### 4.1.1 OCR识别引擎
```python
# 负责文字识别的核心模块
- 车辆识别代号识别
- 发动机号码识别  
- 号牌号码识别
- 轮胎规格识别
```

#### 4.1.2 图像分类引擎
```python
# 负责分类识别的核心模块
- 车辆品牌标志识别
- 车身颜色识别
- 车辆类型识别
```

#### 4.1.3 目标检测引擎
```python
# 负责目标检测的核心模块
- 三角警告牌检测
- 反光背心检测
- 充电接口检测
```

#### 4.1.4 人脸识别引擎
```python
# 负责人脸识别的核心模块
- 查验员身份识别
```

### 4.2 业务服务层设计

#### 4.2.1 识别服务 (RecognitionService)
- 图像预处理
- 调用AI引擎进行识别
- 结果后处理和验证
- 识别结果格式化

#### 4.2.2 比对服务 (ComparisonService)  
- 识别结果与标准数据比对
- 相似度计算
- 比对结果生成

#### 4.2.3 数据服务 (DataService)
- 与公安平台数据交互
- 待比对信息下载
- 识别结果上传

### 4.3 数据模型设计

#### 4.3.1 核心数据表
```sql
-- 识别任务表
CREATE TABLE recognition_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(64) UNIQUE NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 识别结果表  
CREATE TABLE recognition_results (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL,
    recognition_type VARCHAR(50) NOT NULL,
    result_data JSONB NOT NULL,
    confidence FLOAT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆品牌库
CREATE TABLE vehicle_brands (
    id SERIAL PRIMARY KEY,
    brand_name VARCHAR(100) NOT NULL,
    brand_logo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 5. 部署架构

### 5.1 容器化部署
```yaml
# docker-compose.yml 结构
services:
  - api-gateway: Nginx反向代理
  - web-api: FastAPI应用服务
  - ai-worker: AI识别工作进程
  - postgresql: 主数据库
  - redis: 缓存和消息队列
  - minio: 文件存储服务
```

### 5.2 扩展性设计
- 水平扩展: 支持多个AI工作进程
- 负载均衡: Nginx负载均衡
- 异步处理: 基于消息队列的异步识别

## 6. 性能指标

### 6.1 识别准确率要求
- 高精度识别 (≥90%): 车辆识别代号、发动机号码、品牌标志、轮胎规格、安全装置、充电接口、号牌号码、查验员
- 标准精度识别 (≥80%): 车身颜色、车辆类型

### 6.2 性能指标
- 单张图片识别时间: <3秒
- 并发处理能力: 支持10+并发识别任务
- 系统可用性: 99.5%

## 7. 安全设计

### 7.1 数据安全
- 图片数据加密存储
- 识别结果数据脱敏
- API接口鉴权

### 7.2 系统安全
- HTTPS通信
- SQL注入防护
- 访问日志记录

## 8. 开发计划

### 8.1 第一阶段 (基础框架)
- 项目框架搭建
- 基础AI引擎集成
- 核心API开发

### 8.2 第二阶段 (功能实现)
- 各类识别功能实现
- 数据比对功能开发
- 测试和优化

### 8.3 第三阶段 (集成部署)
- 系统集成测试
- 性能优化
- 生产环境部署

## 9. 详细技术实现方案

### 9.1 项目目录结构
```
AutoInspectAI/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py         # 配置管理
│   ├── api/
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── recognition.py  # 识别API
│   │   │   ├── comparison.py   # 比对API
│   │   │   └── data.py         # 数据API
│   ├── core/
│   │   ├── __init__.py
│   │   ├── ai_engines/
│   │   │   ├── __init__.py
│   │   │   ├── ocr_engine.py   # OCR识别引擎
│   │   │   ├── classification_engine.py # 分类引擎
│   │   │   ├── detection_engine.py # 检测引擎
│   │   │   └── face_engine.py  # 人脸识别引擎
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── recognition_service.py
│   │   │   ├── comparison_service.py
│   │   │   └── data_service.py
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── image_processor.py
│   │       └── validators.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── database.py         # 数据库模型
│   │   └── schemas.py          # Pydantic模型
│   └── tests/
│       ├── __init__.py
│       ├── test_recognition.py
│       └── test_comparison.py
├── ai_models/                  # AI模型文件目录
│   ├── ocr/
│   ├── classification/
│   ├── detection/
│   └── face_recognition/
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
├── requirements.txt
├── README.md
└── 需求.md
```

### 9.2 核心AI引擎实现

#### 9.2.1 OCR识别引擎设计
```python
# 支持的OCR识别类型
OCR_TYPES = {
    'vehicle_vin': '车辆识别代号',
    'engine_number': '发动机号码',
    'license_plate': '号牌号码',
    'tire_specification': '轮胎规格'
}

# 每种类型的特定预处理和后处理策略
```

#### 9.2.2 图像分类引擎设计
```python
# 支持的分类类型
CLASSIFICATION_TYPES = {
    'vehicle_brand': '车辆品牌标志',
    'vehicle_color': '车身颜色',
    'vehicle_type': '车辆类型'
}

# 品牌标志库管理
# 颜色分类标准
# 车辆类型GA/T 16.4标准映射
```

#### 9.2.3 目标检测引擎设计
```python
# 支持的检测目标
DETECTION_TARGETS = {
    'warning_triangle': '三角警告牌',
    'reflective_vest': '反光背心',
    'charging_port': '充电接口'
}

# YOLO或其他检测模型集成
```

### 9.3 API接口设计

#### 9.3.1 识别API接口
```python
# POST /api/v1/recognition/recognize
{
    "image_data": "base64编码图片数据",
    "recognition_types": ["vehicle_vin", "vehicle_brand"],
    "image_metadata": {
        "angle": "left_front_45",  # 拍摄角度
        "quality": "high"
    }
}

# 响应格式
{
    "task_id": "uuid",
    "status": "success",
    "results": [
        {
            "type": "vehicle_vin",
            "result": "LSGKB54E8EH123456",
            "confidence": 0.95,
            "bounding_box": [x1, y1, x2, y2]
        }
    ]
}
```

#### 9.3.2 批量识别API
```python
# POST /api/v1/recognition/batch_recognize
{
    "images": [
        {
            "image_data": "base64数据",
            "recognition_types": ["vehicle_vin"],
            "metadata": {...}
        }
    ]
}
```

### 9.4 数据库详细设计

#### 9.4.1 扩展数据表设计
```sql
-- 车辆颜色标准表
CREATE TABLE vehicle_colors (
    id SERIAL PRIMARY KEY,
    color_name VARCHAR(50) NOT NULL,
    color_code VARCHAR(20),
    rgb_range JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆类型标准表 (GA/T 16.4)
CREATE TABLE vehicle_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(20) NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 识别任务详情表
CREATE TABLE task_details (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL,
    image_metadata JSONB,
    processing_time INTEGER, -- 处理时间(毫秒)
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9.5 配置管理设计

#### 9.5.1 环境配置
```python
# config/settings.py
class Settings:
    # 数据库配置
    DATABASE_URL: str = "postgresql://user:pass@localhost/autoinspect"
    REDIS_URL: str = "redis://localhost:6379"

    # AI模型配置
    OCR_MODEL_PATH: str = "./ai_models/ocr/"
    CLASSIFICATION_MODEL_PATH: str = "./ai_models/classification/"

    # 识别阈值配置
    OCR_CONFIDENCE_THRESHOLD: float = 0.8
    CLASSIFICATION_CONFIDENCE_THRESHOLD: float = 0.7

    # 性能配置
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 30  # 秒

    # 外部接口配置
    POLICE_PLATFORM_URL: str = "https://api.police.gov.cn"
    POLICE_PLATFORM_TOKEN: str = "your_token"
```

### 9.6 错误处理和日志设计

#### 9.6.1 错误码定义
```python
ERROR_CODES = {
    1001: "图片格式不支持",
    1002: "图片质量过低",
    1003: "识别置信度过低",
    2001: "AI模型加载失败",
    2002: "识别超时",
    3001: "数据库连接失败",
    3002: "外部接口调用失败"
}
```

#### 9.6.2 日志配置
```python
# 日志级别和格式配置
# 识别任务日志
# 性能监控日志
# 错误异常日志
```

## 10. 测试策略

### 10.1 单元测试
- AI引擎功能测试
- 业务服务逻辑测试
- 数据模型测试

### 10.2 集成测试
- API接口测试
- 数据库集成测试
- 外部接口集成测试

### 10.3 性能测试
- 识别准确率测试 (100张测试图片)
- 并发性能测试
- 内存和CPU使用率测试

### 10.4 用户验收测试
- 各识别功能验收
- 准确率达标验证
- 用户体验测试

## 11. 运维监控

### 11.1 系统监控
- 服务健康检查
- 资源使用监控
- 识别任务监控

### 11.2 日志管理
- 集中化日志收集
- 日志分析和告警
- 审计日志管理

### 11.3 备份策略
- 数据库定期备份
- AI模型文件备份
- 配置文件备份
