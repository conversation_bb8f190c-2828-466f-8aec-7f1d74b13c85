# 车辆智能识别系统 (AutoInspectAI)

## 项目简介

车辆智能识别系统是一个基于深度学习和计算机视觉技术的综合性识别平台，主要用于机动车查验过程中的自动化识别和比对。系统支持多种车辆相关信息的智能识别，包括车辆识别代号、发动机号码、品牌标志、车身颜色等。

## 功能特性

### 🚗 核心识别功能
- **车辆识别代号识别** - 准确率≥90%
- **发动机(驱动电机)号码识别** - 准确率≥90%
- **车辆品牌标志识别** - 准确率≥90%，支持100+品牌
- **车身颜色识别** - 准确率≥80%
- **车辆类型识别比对** - 准确率≥80%，符合GA/T 16.4标准
- **轮胎规格识别** - 准确率≥90%
- **三角警告牌、反光背心识别** - 准确率≥90%
- **充电接口识别** - 准确率≥90%
- **号牌号码识别** - 准确率≥90%
- **查验员识别** - 准确率≥90%

### 🔧 系统特性
- **高性能**: 单张图片识别时间<3秒
- **高并发**: 支持10+并发识别任务
- **可扩展**: 微服务架构，支持水平扩展
- **易部署**: Docker容器化部署
- **监控完善**: 集成Prometheus + Grafana监控

## 技术架构

### 技术栈
- **后端**: Python 3.9+ + FastAPI
- **AI框架**: PyTorch + OpenCV + PaddleOCR + YOLOv8
- **数据库**: PostgreSQL + Redis
- **文件存储**: MinIO
- **消息队列**: Celery + Redis
- **监控**: Prometheus + Grafana
- **部署**: Docker + Docker Compose

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────────────────────────────────────────────────┤
│                    API网关层 (Nginx)                        │
├─────────────────────────────────────────────────────────────┤
│                    业务服务层 (FastAPI)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  识别服务    │ │  比对服务    │ │  数据服务    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    AI引擎层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  OCR引擎     │ │  图像分类    │ │  目标检测    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ PostgreSQL  │ │   MinIO     │ │   Redis     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### 环境要求
- Docker 20.0+
- Docker Compose 2.0+
- Python 3.9+ (开发环境)
- CUDA 11.0+ (GPU加速，可选)

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/AutoInspectAI.git
cd AutoInspectAI
```

2. **准备AI模型**
```bash
# 下载预训练模型到ai_models目录
mkdir -p ai_models/{ocr,classification,detection,face_recognition}
# 将模型文件放置到对应目录
```

3. **启动服务**
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

4. **初始化数据库**
```bash
# 运行数据库迁移
docker-compose exec web-api alembic upgrade head

# 导入基础数据
docker-compose exec web-api python scripts/init_data.py
```

5. **访问服务**
- API文档: http://localhost:8000/docs
- 任务监控: http://localhost:5555
- 系统监控: http://localhost:3000 (Grafana)
- 指标监控: http://localhost:9090 (Prometheus)

### 开发环境设置

1. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

4. **启动开发服务器**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## API使用示例

### 单张图片识别
```python
import requests
import base64

# 读取图片并编码
with open('test_image.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read()).decode()

# 调用识别API
response = requests.post('http://localhost:8000/api/v1/recognition/recognize', json={
    'image_data': image_data,
    'recognition_types': ['vehicle_vin', 'vehicle_brand', 'vehicle_color'],
    'image_metadata': {
        'angle': 'left_front_45',
        'quality': 'high'
    }
})

result = response.json()
print(result)
```

### 批量识别
```python
# 批量识别多张图片
images = []
for i in range(5):
    with open(f'test_image_{i}.jpg', 'rb') as f:
        image_data = base64.b64encode(f.read()).decode()
        images.append({
            'image_data': image_data,
            'recognition_types': ['vehicle_vin'],
            'metadata': {'batch_id': f'batch_{i}'}
        })

response = requests.post('http://localhost:8000/api/v1/recognition/batch_recognize', json={
    'images': images
})

results = response.json()
```

## 项目结构

```
AutoInspectAI/
├── app/                        # 应用主目录
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config/                 # 配置管理
│   ├── api/                    # API路由
│   ├── core/                   # 核心业务逻辑
│   │   ├── ai_engines/         # AI引擎
│   │   ├── services/           # 业务服务
│   │   └── utils/              # 工具函数
│   ├── models/                 # 数据模型
│   └── tests/                  # 测试代码
├── ai_models/                  # AI模型文件
├── docker/                     # Docker配置
├── scripts/                    # 脚本文件
├── docs/                       # 文档
├── requirements.txt            # Python依赖
├── docker-compose.yml          # Docker编排
└── README.md                   # 项目说明
```

## 测试

### 运行单元测试
```bash
pytest app/tests/ -v --cov=app
```

### 运行集成测试
```bash
pytest app/tests/integration/ -v
```

### 性能测试
```bash
# 使用locust进行压力测试
locust -f tests/performance/locustfile.py --host=http://localhost:8000
```

## 监控和运维

### 日志查看
```bash
# 查看应用日志
docker-compose logs -f web-api

# 查看AI工作进程日志
docker-compose logs -f ai-worker
```

### 性能监控
- 访问 http://localhost:3000 查看Grafana仪表板
- 默认用户名/密码: admin/admin

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8000/health

# 检查AI引擎状态
curl http://localhost:8000/api/v1/system/status
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: [Your Name](mailto:<EMAIL>)
- 项目地址: https://github.com/your-org/AutoInspectAI
- 问题反馈: https://github.com/your-org/AutoInspectAI/issues
