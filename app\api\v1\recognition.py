"""
识别服务API路由
"""
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Optional
import base64
import logging
import time

from app.models.schemas import (
    RecognitionRequest,
    RecognitionResponse,
    BatchRecognitionRequest,
    BatchRecognitionResponse
)
from app.core.services.classification_service import classification_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/recognize", response_model=RecognitionResponse)
async def recognize_image(request: RecognitionRequest):
    """
    单张图片识别
    
    支持的识别类型：
    - vehicle_vin: 车辆识别代号
    - engine_number: 发动机号码
    - license_plate: 号牌号码
    - tire_specification: 轮胎规格
    - vehicle_brand: 车辆品牌标志
    - vehicle_color: 车身颜色
    - vehicle_type: 车辆类型
    - warning_triangle: 三角警告牌
    - reflective_vest: 反光背心
    - charging_port: 充电接口
    - inspector_face: 查验员识别
    """
    try:
        logger.info(f"收到识别请求，类型: {request.recognition_types}")

        results = []

        # 处理每种识别类型
        for recognition_type in request.recognition_types:
            try:
                if recognition_type == "vehicle_brand":
                    # 车辆品牌识别
                    result = await classification_service.classify_vehicle_brand(request.image_data)
                    if result['success'] and result['best_match']:
                        results.append({
                            "type": recognition_type,
                            "result": result['best_match']['brand'],
                            "confidence": result['best_match']['confidence'],
                            "bbox": None,
                            "additional_info": {
                                "all_candidates": result['results'][:3],  # 前3个候选
                                "processing_time": result['processing_time']
                            }
                        })
                    else:
                        results.append({
                            "type": recognition_type,
                            "result": "识别失败",
                            "confidence": 0.0,
                            "bbox": None,
                            "error": result.get('error', '未知错误')
                        })

                elif recognition_type == "vehicle_color":
                    # 车身颜色识别
                    result = await classification_service.classify_vehicle_color(request.image_data)
                    if result['success'] and result['best_match']:
                        results.append({
                            "type": recognition_type,
                            "result": result['best_match']['color'],
                            "confidence": result['best_match']['confidence'],
                            "bbox": None,
                            "additional_info": {
                                "all_candidates": result['results'][:3],
                                "processing_time": result['processing_time']
                            }
                        })
                    else:
                        results.append({
                            "type": recognition_type,
                            "result": "识别失败",
                            "confidence": 0.0,
                            "bbox": None,
                            "error": result.get('error', '未知错误')
                        })

                elif recognition_type == "vehicle_type":
                    # 车辆类型识别
                    result = await classification_service.classify_vehicle_type(request.image_data)
                    if result['success'] and result['best_match']:
                        results.append({
                            "type": recognition_type,
                            "result": f"{result['best_match']['type_code']} - {result['best_match']['type_name']}",
                            "confidence": result['best_match']['confidence'],
                            "bbox": None,
                            "additional_info": {
                                "type_code": result['best_match']['type_code'],
                                "type_name": result['best_match']['type_name'],
                                "standard": "GA/T 16.4",
                                "all_candidates": result['results'][:3],
                                "processing_time": result['processing_time']
                            }
                        })
                    else:
                        results.append({
                            "type": recognition_type,
                            "result": "识别失败",
                            "confidence": 0.0,
                            "bbox": None,
                            "error": result.get('error', '未知错误')
                        })

                else:
                    # 其他识别类型（暂未实现）
                    results.append({
                        "type": recognition_type,
                        "result": f"待实现_{recognition_type}",
                        "confidence": 0.0,
                        "bbox": None,
                        "note": "该识别类型尚未实现"
                    })

            except Exception as e:
                logger.error(f"处理识别类型 {recognition_type} 时出错: {str(e)}")
                results.append({
                    "type": recognition_type,
                    "result": "处理失败",
                    "confidence": 0.0,
                    "bbox": None,
                    "error": str(e)
                })

        return RecognitionResponse(
            task_id=f"task_{int(time.time() * 1000)}",
            status="success",
            results=results
        )
        
    except Exception as e:
        logger.error(f"识别请求处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")


@router.post("/batch_recognize", response_model=BatchRecognitionResponse)
async def batch_recognize_images(request: BatchRecognitionRequest):
    """
    批量图片识别
    """
    try:
        logger.info(f"收到批量识别请求，图片数量: {len(request.images)}")
        
        # TODO: 实现批量识别逻辑
        # 1. 遍历所有图片
        # 2. 并行处理识别任务
        # 3. 返回所有结果
        
        # 临时返回示例数据
        results = []
        for i, image_request in enumerate(request.images):
            results.append({
                "image_index": i,
                "task_id": f"batch-task-{i}",
                "status": "success",
                "results": [
                    {
                        "type": recognition_type,
                        "result": f"批量示例结果_{recognition_type}_{i}",
                        "confidence": 0.90,
                        "bbox": [100, 100, 200, 150]
                    }
                    for recognition_type in image_request.recognition_types
                ]
            })
        
        return BatchRecognitionResponse(
            batch_id="temp-batch-id",
            total_images=len(request.images),
            completed_images=len(request.images),
            results=results
        )
        
    except Exception as e:
        logger.error(f"批量识别请求处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量识别失败: {str(e)}")


@router.post("/upload_recognize")
async def upload_and_recognize(
    file: UploadFile = File(...),
    recognition_types: str = Form(...),
    image_angle: Optional[str] = Form(None)
):
    """
    上传文件并识别
    """
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 转换为base64
        image_data = base64.b64encode(file_content).decode()
        
        # 解析识别类型
        recognition_types_list = [t.strip() for t in recognition_types.split(',')]
        
        # 构造识别请求
        request = RecognitionRequest(
            image_data=image_data,
            recognition_types=recognition_types_list,
            image_metadata={
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(file_content),
                "angle": image_angle
            }
        )
        
        # 调用识别接口
        return await recognize_image(request)
        
    except Exception as e:
        logger.error(f"文件上传识别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传识别失败: {str(e)}")


@router.get("/task/{task_id}")
async def get_recognition_task(task_id: str):
    """
    获取识别任务状态和结果
    """
    try:
        # TODO: 从数据库查询任务状态
        logger.info(f"查询识别任务: {task_id}")
        
        # 临时返回示例数据
        return {
            "task_id": task_id,
            "status": "completed",
            "created_at": "2024-01-01T00:00:00Z",
            "completed_at": "2024-01-01T00:00:05Z",
            "results": [
                {
                    "type": "vehicle_vin",
                    "result": "LSGKB54E8EH123456",
                    "confidence": 0.95,
                    "bbox": [100, 100, 300, 150]
                }
            ]
        }
        
    except Exception as e:
        logger.error(f"查询识别任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务失败: {str(e)}")


@router.get("/supported_types")
async def get_supported_recognition_types():
    """
    获取支持的识别类型
    """
    from app.config.settings import RECOGNITION_TYPES
    
    return {
        "supported_types": [
            {
                "type": key,
                "name": value["name"],
                "engine": value["engine"],
                "confidence_threshold": value["confidence_threshold"],
                "description": value["description"]
            }
            for key, value in RECOGNITION_TYPES.items()
        ]
    }
