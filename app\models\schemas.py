"""
Pydantic数据模型定义
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import base64


class ImageMetadata(BaseModel):
    """图片元数据"""
    filename: Optional[str] = None
    content_type: Optional[str] = None
    size: Optional[int] = None
    angle: Optional[str] = None  # 拍摄角度
    quality: Optional[str] = None  # 图片质量
    timestamp: Optional[datetime] = None


class RecognitionResult(BaseModel):
    """识别结果"""
    type: str = Field(..., description="识别类型")
    result: str = Field(..., description="识别结果")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    bbox: Optional[List[int]] = Field(None, description="边界框坐标 [x1, y1, x2, y2]")
    additional_info: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class RecognitionRequest(BaseModel):
    """识别请求"""
    image_data: str = Field(..., description="Base64编码的图片数据")
    recognition_types: List[str] = Field(..., description="识别类型列表")
    image_metadata: Optional[ImageMetadata] = Field(None, description="图片元数据")
    
    @validator('image_data')
    def validate_image_data(cls, v):
        """验证图片数据格式"""
        try:
            # 尝试解码base64数据
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError('无效的Base64图片数据')
    
    @validator('recognition_types')
    def validate_recognition_types(cls, v):
        """验证识别类型"""
        from app.config.settings import RECOGNITION_TYPES
        
        valid_types = set(RECOGNITION_TYPES.keys())
        invalid_types = set(v) - valid_types
        
        if invalid_types:
            raise ValueError(f'不支持的识别类型: {invalid_types}')
        
        return v


class RecognitionResponse(BaseModel):
    """识别响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    results: List[RecognitionResult] = Field(..., description="识别结果列表")
    error_message: Optional[str] = Field(None, description="错误信息")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")


class BatchImageRequest(BaseModel):
    """批量识别中的单个图片请求"""
    image_data: str = Field(..., description="Base64编码的图片数据")
    recognition_types: List[str] = Field(..., description="识别类型列表")
    metadata: Optional[Dict[str, Any]] = Field(None, description="图片元数据")


class BatchRecognitionRequest(BaseModel):
    """批量识别请求"""
    images: List[BatchImageRequest] = Field(..., description="图片列表")
    batch_metadata: Optional[Dict[str, Any]] = Field(None, description="批次元数据")
    
    @validator('images')
    def validate_images_count(cls, v):
        """验证图片数量"""
        if len(v) > 100:  # 限制批量处理的图片数量
            raise ValueError('批量处理图片数量不能超过100张')
        return v


class BatchRecognitionResult(BaseModel):
    """批量识别单个结果"""
    image_index: int = Field(..., description="图片索引")
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="处理状态")
    results: List[RecognitionResult] = Field(..., description="识别结果")
    error_message: Optional[str] = Field(None, description="错误信息")


class BatchRecognitionResponse(BaseModel):
    """批量识别响应"""
    batch_id: str = Field(..., description="批次ID")
    total_images: int = Field(..., description="总图片数")
    completed_images: int = Field(..., description="已完成图片数")
    results: List[BatchRecognitionResult] = Field(..., description="识别结果列表")
    batch_status: str = Field(default="processing", description="批次状态")


class ComparisonRequest(BaseModel):
    """比对请求"""
    recognition_results: List[RecognitionResult] = Field(..., description="识别结果")
    reference_data: Dict[str, Any] = Field(..., description="参考数据")
    comparison_types: List[str] = Field(..., description="比对类型")


class ComparisonResult(BaseModel):
    """比对结果"""
    type: str = Field(..., description="比对类型")
    match: bool = Field(..., description="是否匹配")
    similarity: float = Field(..., ge=0.0, le=1.0, description="相似度")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


class ComparisonResponse(BaseModel):
    """比对响应"""
    task_id: str = Field(..., description="任务ID")
    results: List[ComparisonResult] = Field(..., description="比对结果")
    overall_match: bool = Field(..., description="整体匹配结果")


class TaskStatus(BaseModel):
    """任务状态"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="进度")
    error_message: Optional[str] = Field(None, description="错误信息")


class SystemStatus(BaseModel):
    """系统状态"""
    system: Dict[str, str] = Field(..., description="系统资源状态")
    application: Dict[str, Any] = Field(..., description="应用状态")
    ai_engines: Dict[str, str] = Field(..., description="AI引擎状态")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: bool = Field(True, description="是否为错误")
    error_code: Optional[int] = Field(None, description="错误码")
    message: str = Field(..., description="错误信息")
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class SuccessResponse(BaseModel):
    """成功响应"""
    success: bool = Field(True, description="是否成功")
    message: str = Field(..., description="成功信息")
    data: Optional[Any] = Field(None, description="响应数据")
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


# 数据库模型相关的Pydantic模型
class VehicleBrandBase(BaseModel):
    """车辆品牌基础模型"""
    brand_name: str = Field(..., description="品牌名称")
    brand_logo_path: Optional[str] = Field(None, description="品牌标志路径")


class VehicleBrandCreate(VehicleBrandBase):
    """创建车辆品牌"""
    pass


class VehicleBrand(VehicleBrandBase):
    """车辆品牌模型"""
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class VehicleColorBase(BaseModel):
    """车辆颜色基础模型"""
    color_name: str = Field(..., description="颜色名称")
    color_code: Optional[str] = Field(None, description="颜色代码")
    rgb_range: Optional[Dict[str, Any]] = Field(None, description="RGB范围")


class VehicleColor(VehicleColorBase):
    """车辆颜色模型"""
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class VehicleTypeBase(BaseModel):
    """车辆类型基础模型"""
    type_code: str = Field(..., description="类型代码")
    type_name: str = Field(..., description="类型名称")
    category: Optional[str] = Field(None, description="类别")
    description: Optional[str] = Field(None, description="描述")


class VehicleType(VehicleTypeBase):
    """车辆类型模型"""
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True
