# 图像分类引擎文档

## 概述

图像分类引擎是AutoInspectAI系统的核心组件之一，负责车辆相关的图像分类任务。该引擎支持三种主要的分类功能：

1. **车辆品牌识别** - 识别车辆的品牌标志
2. **车身颜色识别** - 识别车辆的主要颜色
3. **车辆类型识别** - 根据GA/T 16.4标准识别车辆类型

## 架构设计

### 核心组件

```
分类引擎架构
├── BaseAIEngine (抽象基类)
│   ├── 图像验证
│   ├── 预处理管道
│   └── 模型信息管理
├── TorchBaseEngine (PyTorch基类)
│   ├── 设备管理
│   ├── 模型加载/卸载
│   └── 推理优化
└── 具体分类器
    ├── VehicleBrandClassifier (品牌分类)
    ├── VehicleColorClassifier (颜色分类)
    └── VehicleTypeClassifier (类型分类)
```

### 服务层

- **ClassificationService**: 业务逻辑层，提供高级API
- **ClassificationEngineManager**: 引擎管理器，统一管理所有分类器

## 功能特性

### 1. 车辆品牌识别

- **模型架构**: ResNet50
- **支持品牌**: 50+个主流汽车品牌
- **输入要求**: 224x224x3 RGB图像
- **输出格式**: 品牌名称 + 置信度分数

**支持的品牌列表**:
```
奥迪、宝马、奔驰、大众、丰田、本田、日产、现代、起亚、福特、
雪佛兰、别克、凯迪拉克、沃尔沃、捷豹、路虎、保时捷、法拉利、
兰博基尼、玛莎拉蒂、比亚迪、吉利、长城、奇瑞、长安等
```

### 2. 车身颜色识别

- **模型架构**: ResNet34
- **支持颜色**: 16种常见车身颜色
- **输入要求**: 224x224x3 RGB图像
- **输出格式**: 颜色名称 + 置信度分数

**支持的颜色**:
```
白色、黑色、银色、灰色、红色、蓝色、绿色、黄色、
棕色、橙色、紫色、粉色、金色、米色、深蓝色、深绿色
```

### 3. 车辆类型识别

- **模型架构**: ResNet101
- **分类标准**: GA/T 16.4
- **支持类型**: 18种车辆类型
- **输入要求**: 224x224x3 RGB图像
- **输出格式**: 类型代码 + 类型名称 + 置信度分数

**GA/T 16.4 车辆类型**:
```
K11: 小型载客汽车    K12: 中型载客汽车    K13: 大型载客汽车
K21: 小型载货汽车    K22: 中型载货汽车    K23: 重型载货汽车
K31: 小型专项作业车  K32: 中型专项作业车  K33: 重型专项作业车
K41: 轻型牵引车      K42: 中型牵引车      K43: 重型牵引车
M1:  乘用车          M2:  轻型客车        M3:  大型客车
N1:  轻型货车        N2:  中型货车        N3:  重型货车
```

## 使用方法

### 1. 初始化服务

```python
from app.core.services.classification_service import classification_service

# 初始化分类服务
device = "cuda"  # 或 "cpu"
success = await classification_service.initialize_service(device)
```

### 2. 单个分类

```python
import base64

# 准备图像数据 (base64编码)
with open("vehicle_image.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode()

# 品牌识别
result = await classification_service.classify_vehicle_brand(image_data)

# 颜色识别
result = await classification_service.classify_vehicle_color(image_data)

# 类型识别
result = await classification_service.classify_vehicle_type(image_data)
```

### 3. 批量分类

```python
requests = [
    {
        'image_data': image_data_1,
        'type': 'vehicle_brand',
        'kwargs': {'top_k': 5}
    },
    {
        'image_data': image_data_2,
        'type': 'vehicle_color',
        'kwargs': {}
    }
]

results = await classification_service.batch_classify(requests)
```

### 4. API调用

```bash
# 通过REST API调用
curl -X POST "http://localhost:8000/api/v1/recognition/recognize" \
  -H "Content-Type: application/json" \
  -d '{
    "image_data": "base64_encoded_image_data",
    "recognition_types": ["vehicle_brand", "vehicle_color", "vehicle_type"]
  }'
```

## 配置参数

### 置信度阈值

```python
# 在 app/config/settings.py 中配置
CLASSIFICATION_CONFIDENCE_THRESHOLD = 0.7  # 默认置信度阈值
```

### 模型路径

```python
# 模型文件路径配置
CLASSIFICATION_MODEL_PATH = "ai_models/classification"
```

### 设备配置

```python
USE_GPU = True  # 是否使用GPU加速
```

## 性能指标

### 准确率要求

- **品牌识别**: ≥ 85%
- **颜色识别**: ≥ 90%
- **类型识别**: ≥ 80%

### 性能指标

- **推理速度**: < 100ms (GPU) / < 500ms (CPU)
- **内存占用**: < 2GB (所有模型加载)
- **并发支持**: 支持多线程并发推理

## 错误处理

### 异常类型

```python
from app.core.utils.exceptions import AIEngineException, ImageProcessingException

# AI引擎异常
AIEngineException("模型加载失败", 2001)

# 图像处理异常
ImageProcessingException("图像解码失败", 1004)
```

### 常见错误

1. **模型文件不存在**: 检查模型路径配置
2. **图像格式不支持**: 确保图像为RGB格式
3. **内存不足**: 减少批处理大小或使用CPU
4. **置信度过低**: 调整置信度阈值或改善图像质量

## 测试和验证

### 运行测试

```bash
# 初始化模型文件
python scripts/init_classification_models.py

# 运行功能测试
python scripts/test_classification.py

# 运行单元测试
python -m pytest app/tests/test_classification_engine.py -v
```

### 测试覆盖

- 模型初始化测试
- 图像预处理测试
- 分类功能测试
- 错误处理测试
- 性能基准测试

## 部署建议

### 生产环境

1. **使用GPU**: 配置CUDA环境以获得最佳性能
2. **模型优化**: 考虑使用TensorRT或ONNX优化模型
3. **负载均衡**: 部署多个实例以处理高并发
4. **监控告警**: 监控模型性能和错误率

### 模型更新

1. **版本管理**: 使用版本号管理模型文件
2. **A/B测试**: 新模型上线前进行A/B测试
3. **回滚机制**: 准备模型回滚方案
4. **性能监控**: 持续监控模型性能指标

## 扩展开发

### 添加新的分类器

1. 继承 `TorchBaseEngine` 基类
2. 实现 `load_model()` 和 `predict()` 方法
3. 在 `ClassificationEngineManager` 中注册
4. 添加相应的服务层接口

### 自定义预处理

```python
def _setup_transforms(self):
    """自定义图像预处理管道"""
    self.transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                           std=[0.229, 0.224, 0.225])
    ])
```

## 常见问题

**Q: 如何提高识别准确率？**
A: 1) 使用高质量的训练数据 2) 调整模型架构 3) 优化预处理流程 4) 使用数据增强技术

**Q: 如何处理新的车辆品牌？**
A: 1) 收集新品牌的训练数据 2) 重新训练模型 3) 更新标签文件 4) 部署新模型

**Q: 如何优化推理速度？**
A: 1) 使用GPU加速 2) 模型量化 3) 批处理推理 4) 模型剪枝

**Q: 如何处理模糊或低质量图像？**
A: 1) 图像质量检测 2) 图像增强预处理 3) 多尺度推理 4) 置信度阈值调整
