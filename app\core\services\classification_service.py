"""
分类服务
提供车辆品牌、颜色、类型分类的业务逻辑
"""
import base64
import io
import time
from typing import Dict, Any, List, Optional
import logging
import numpy as np
import cv2
from PIL import Image

from app.core.ai_engines.classification_engine import classification_manager
from app.core.utils.exceptions import AIEngineException, ImageProcessingException
from app.config.settings import settings

logger = logging.getLogger(__name__)


class ClassificationService:
    """分类服务类"""
    
    def __init__(self):
        self.manager = classification_manager
        self.confidence_thresholds = {
            'vehicle_brand': settings.CLASSIFICATION_CONFIDENCE_THRESHOLD,
            'vehicle_color': settings.CLASSIFICATION_CONFIDENCE_THRESHOLD,
            'vehicle_type': settings.CLASSIFICATION_CONFIDENCE_THRESHOLD
        }
    
    async def initialize_service(self, device: str = "cpu") -> bool:
        """
        初始化分类服务
        
        Args:
            device: 计算设备
            
        Returns:
            初始化是否成功
        """
        try:
            success = self.manager.initialize(device)
            if success:
                logger.info("分类服务初始化成功")
            else:
                logger.error("分类服务初始化失败")
            return success
        except Exception as e:
            logger.error(f"分类服务初始化异常: {str(e)}")
            return False
    
    def decode_image(self, image_data: str) -> np.ndarray:
        """
        解码base64图像数据
        
        Args:
            image_data: base64编码的图像数据
            
        Returns:
            解码后的图像数组
        """
        try:
            # 解码base64数据
            image_bytes = base64.b64decode(image_data)
            
            # 转换为numpy数组
            image_array = np.frombuffer(image_bytes, dtype=np.uint8)
            
            # 解码图像
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ImageProcessingException("图像解码失败", 1004)
            
            return image
            
        except Exception as e:
            logger.error(f"图像解码失败: {str(e)}")
            raise ImageProcessingException(f"图像解码失败: {str(e)}", 1004)
    
    def validate_image_quality(self, image: np.ndarray) -> Dict[str, Any]:
        """
        验证图像质量
        
        Args:
            image: 输入图像
            
        Returns:
            质量评估结果
        """
        try:
            height, width = image.shape[:2]
            
            # 检查图像尺寸
            min_size = 100
            if height < min_size or width < min_size:
                return {
                    'valid': False,
                    'reason': f'图像尺寸过小，最小尺寸要求: {min_size}x{min_size}',
                    'size': (width, height)
                }
            
            # 检查图像清晰度（使用拉普拉斯算子）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            blur_threshold = 100  # 模糊阈值
            if laplacian_var < blur_threshold:
                return {
                    'valid': False,
                    'reason': f'图像过于模糊，清晰度分数: {laplacian_var:.2f}',
                    'blur_score': laplacian_var
                }
            
            # 检查图像亮度
            mean_brightness = np.mean(gray)
            if mean_brightness < 30:
                return {
                    'valid': False,
                    'reason': f'图像过暗，平均亮度: {mean_brightness:.2f}',
                    'brightness': mean_brightness
                }
            elif mean_brightness > 225:
                return {
                    'valid': False,
                    'reason': f'图像过亮，平均亮度: {mean_brightness:.2f}',
                    'brightness': mean_brightness
                }
            
            return {
                'valid': True,
                'size': (width, height),
                'blur_score': laplacian_var,
                'brightness': mean_brightness
            }
            
        except Exception as e:
            logger.error(f"图像质量验证失败: {str(e)}")
            return {
                'valid': False,
                'reason': f'质量验证失败: {str(e)}'
            }
    
    async def classify_vehicle_brand(self, image_data: str, **kwargs) -> Dict[str, Any]:
        """
        车辆品牌分类
        
        Args:
            image_data: base64编码的图像数据
            **kwargs: 其他参数
            
        Returns:
            分类结果
        """
        try:
            start_time = time.time()
            
            # 解码图像
            image = self.decode_image(image_data)
            
            # 验证图像质量
            quality_check = self.validate_image_quality(image)
            if not quality_check['valid']:
                raise ImageProcessingException(quality_check['reason'], 1003)
            
            # 执行品牌分类
            result = self.manager.classify_brand(image, **kwargs)
            
            # 应用置信度阈值
            threshold = self.confidence_thresholds['vehicle_brand']
            filtered_results = [
                r for r in result['results'] 
                if r['confidence'] >= threshold
            ]
            
            if not filtered_results:
                logger.warning(f"品牌识别置信度过低，最高置信度: {result['results'][0]['confidence']:.3f}")
            
            total_time = time.time() - start_time
            
            return {
                'success': True,
                'type': 'vehicle_brand',
                'results': filtered_results,
                'best_match': filtered_results[0] if filtered_results else None,
                'all_results': result['results'],  # 包含所有结果用于调试
                'confidence_threshold': threshold,
                'quality_info': quality_check,
                'processing_time': total_time,
                'model_info': result.get('model_info', {})
            }
            
        except Exception as e:
            logger.error(f"品牌分类失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'type': 'vehicle_brand'
            }
    
    async def classify_vehicle_color(self, image_data: str, **kwargs) -> Dict[str, Any]:
        """
        车身颜色分类
        
        Args:
            image_data: base64编码的图像数据
            **kwargs: 其他参数
            
        Returns:
            分类结果
        """
        try:
            start_time = time.time()
            
            # 解码图像
            image = self.decode_image(image_data)
            
            # 验证图像质量
            quality_check = self.validate_image_quality(image)
            if not quality_check['valid']:
                raise ImageProcessingException(quality_check['reason'], 1003)
            
            # 执行颜色分类
            result = self.manager.classify_color(image, **kwargs)
            
            # 应用置信度阈值
            threshold = self.confidence_thresholds['vehicle_color']
            filtered_results = [
                r for r in result['results'] 
                if r['confidence'] >= threshold
            ]
            
            if not filtered_results:
                logger.warning(f"颜色识别置信度过低，最高置信度: {result['results'][0]['confidence']:.3f}")
            
            total_time = time.time() - start_time
            
            return {
                'success': True,
                'type': 'vehicle_color',
                'results': filtered_results,
                'best_match': filtered_results[0] if filtered_results else None,
                'all_results': result['results'],
                'confidence_threshold': threshold,
                'quality_info': quality_check,
                'processing_time': total_time,
                'model_info': result.get('model_info', {})
            }
            
        except Exception as e:
            logger.error(f"颜色分类失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'type': 'vehicle_color'
            }
    
    async def classify_vehicle_type(self, image_data: str, **kwargs) -> Dict[str, Any]:
        """
        车辆类型分类
        
        Args:
            image_data: base64编码的图像数据
            **kwargs: 其他参数
            
        Returns:
            分类结果
        """
        try:
            start_time = time.time()
            
            # 解码图像
            image = self.decode_image(image_data)
            
            # 验证图像质量
            quality_check = self.validate_image_quality(image)
            if not quality_check['valid']:
                raise ImageProcessingException(quality_check['reason'], 1003)
            
            # 执行类型分类
            result = self.manager.classify_type(image, **kwargs)
            
            # 应用置信度阈值
            threshold = self.confidence_thresholds['vehicle_type']
            filtered_results = [
                r for r in result['results'] 
                if r['confidence'] >= threshold
            ]
            
            if not filtered_results:
                logger.warning(f"类型识别置信度过低，最高置信度: {result['results'][0]['confidence']:.3f}")
            
            total_time = time.time() - start_time
            
            return {
                'success': True,
                'type': 'vehicle_type',
                'results': filtered_results,
                'best_match': filtered_results[0] if filtered_results else None,
                'all_results': result['results'],
                'confidence_threshold': threshold,
                'quality_info': quality_check,
                'processing_time': total_time,
                'model_info': result.get('model_info', {}),
                'standard': 'GA/T 16.4'
            }
            
        except Exception as e:
            logger.error(f"类型分类失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'type': 'vehicle_type'
            }
    
    async def batch_classify(self, classification_requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量分类
        
        Args:
            classification_requests: 分类请求列表
            
        Returns:
            分类结果列表
        """
        results = []
        
        for i, request in enumerate(classification_requests):
            try:
                image_data = request.get('image_data')
                classification_type = request.get('type')
                kwargs = request.get('kwargs', {})
                
                if classification_type == 'vehicle_brand':
                    result = await self.classify_vehicle_brand(image_data, **kwargs)
                elif classification_type == 'vehicle_color':
                    result = await self.classify_vehicle_color(image_data, **kwargs)
                elif classification_type == 'vehicle_type':
                    result = await self.classify_vehicle_type(image_data, **kwargs)
                else:
                    result = {
                        'success': False,
                        'error': f'不支持的分类类型: {classification_type}',
                        'type': classification_type
                    }
                
                result['request_index'] = i
                results.append(result)
                
            except Exception as e:
                logger.error(f"批量分类第{i}个请求失败: {str(e)}")
                results.append({
                    'success': False,
                    'error': str(e),
                    'request_index': i
                })
        
        return results
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'service_name': 'ClassificationService',
            'is_initialized': self.manager.is_initialized,
            'confidence_thresholds': self.confidence_thresholds,
            'engines_status': self.manager.get_status()
        }


# 全局分类服务实例
classification_service = ClassificationService()
