"""
应用配置管理
"""
from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "AutoInspectAI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://autoinspect:password@localhost:5432/autoinspect_db"
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_CACHE_TTL: int = 3600
    
    # 文件存储配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_BUCKET_NAME: str = "autoinspect-images"
    MINIO_SECURE: bool = False
    
    # AI模型配置
    AI_MODELS_PATH: str = "./ai_models"
    OCR_MODEL_PATH: str = "./ai_models/ocr/"
    CLASSIFICATION_MODEL_PATH: str = "./ai_models/classification/"
    DETECTION_MODEL_PATH: str = "./ai_models/detection/"
    FACE_MODEL_PATH: str = "./ai_models/face_recognition/"
    
    # 识别阈值配置
    OCR_CONFIDENCE_THRESHOLD: float = 0.8
    CLASSIFICATION_CONFIDENCE_THRESHOLD: float = 0.7
    DETECTION_CONFIDENCE_THRESHOLD: float = 0.6
    FACE_CONFIDENCE_THRESHOLD: float = 0.8
    
    # 性能配置
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 30
    MAX_IMAGE_SIZE: int = 10485760  # 10MB
    SUPPORTED_IMAGE_FORMATS: str = "jpg,jpeg,png,bmp"
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: str = "json"
    CELERY_TIMEZONE: str = "Asia/Shanghai"
    
    # 外部接口配置
    POLICE_PLATFORM_URL: str = "https://api.police.gov.cn"
    POLICE_PLATFORM_TOKEN: str = ""
    POLICE_PLATFORM_TIMEOUT: int = 30
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # 监控配置
    PROMETHEUS_ENABLED: bool = True
    PROMETHEUS_PORT: int = 8001
    
    # 日志配置
    LOG_FILE_PATH: str = "./logs/app.log"
    LOG_MAX_SIZE: str = "100MB"
    LOG_BACKUP_COUNT: int = 5
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @property
    def supported_image_formats_list(self) -> List[str]:
        """获取支持的图片格式列表"""
        return [fmt.strip().lower() for fmt in self.SUPPORTED_IMAGE_FORMATS.split(',')]
    
    @property
    def database_url_async(self) -> str:
        """获取异步数据库连接URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()


# 识别类型配置
RECOGNITION_TYPES = {
    'vehicle_vin': {
        'name': '车辆识别代号',
        'engine': 'ocr',
        'confidence_threshold': 0.9,
        'description': '17位车辆识别代号识别'
    },
    'engine_number': {
        'name': '发动机号码',
        'engine': 'ocr',
        'confidence_threshold': 0.9,
        'description': '发动机或驱动电机号码识别'
    },
    'license_plate': {
        'name': '号牌号码',
        'engine': 'ocr',
        'confidence_threshold': 0.9,
        'description': '车辆号牌号码识别'
    },
    'tire_specification': {
        'name': '轮胎规格',
        'engine': 'ocr',
        'confidence_threshold': 0.9,
        'description': '轮胎规格参数识别'
    },
    'vehicle_brand': {
        'name': '车辆品牌标志',
        'engine': 'classification',
        'confidence_threshold': 0.9,
        'description': '车辆品牌标志分类识别'
    },
    'vehicle_color': {
        'name': '车身颜色',
        'engine': 'classification',
        'confidence_threshold': 0.8,
        'description': '车身颜色分类识别'
    },
    'vehicle_type': {
        'name': '车辆类型',
        'engine': 'classification',
        'confidence_threshold': 0.8,
        'description': '车辆类型分类识别'
    },
    'warning_triangle': {
        'name': '三角警告牌',
        'engine': 'detection',
        'confidence_threshold': 0.9,
        'description': '三角警告牌检测'
    },
    'reflective_vest': {
        'name': '反光背心',
        'engine': 'detection',
        'confidence_threshold': 0.9,
        'description': '反光背心检测'
    },
    'charging_port': {
        'name': '充电接口',
        'engine': 'detection',
        'confidence_threshold': 0.9,
        'description': '新能源汽车充电接口检测'
    },
    'inspector_face': {
        'name': '查验员识别',
        'engine': 'face',
        'confidence_threshold': 0.9,
        'description': '查验员人脸识别'
    }
}


# 图片拍摄角度配置
IMAGE_ANGLES = {
    'left_front_45': '车辆左前方斜视45度',
    'right_rear_45': '车辆右后方斜视45度',
    'front_view': '车辆正前方',
    'rear_view': '车辆正后方',
    'side_view': '车辆侧面',
    'license_photo': '行驶证图片',
    'nameplate': '车辆标牌',
    'tire_side': '轮胎侧面',
    'charging_port_area': '充电接口区域',
    'inspector_photo': '查验员照片'
}


# 错误码定义
ERROR_CODES = {
    # 图片相关错误 1000-1999
    1001: "图片格式不支持",
    1002: "图片大小超出限制",
    1003: "图片质量过低",
    1004: "图片解码失败",
    1005: "图片为空或损坏",
    
    # AI识别相关错误 2000-2999
    2001: "AI模型加载失败",
    2002: "识别任务超时",
    2003: "识别置信度过低",
    2004: "识别结果为空",
    2005: "不支持的识别类型",
    
    # 数据库相关错误 3000-3999
    3001: "数据库连接失败",
    3002: "数据查询失败",
    3003: "数据保存失败",
    3004: "数据不存在",
    
    # 外部接口相关错误 4000-4999
    4001: "外部接口调用失败",
    4002: "接口认证失败",
    4003: "接口响应超时",
    4004: "接口返回数据格式错误",
    
    # 系统相关错误 5000-5999
    5001: "系统内部错误",
    5002: "服务不可用",
    5003: "资源不足",
    5004: "配置错误",
    5005: "权限不足"
}


def get_error_message(error_code: int) -> str:
    """获取错误码对应的错误信息"""
    return ERROR_CODES.get(error_code, "未知错误")


# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": settings.LOG_FORMAT,
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": settings.LOG_LEVEL,
            "formatter": "default",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": settings.LOG_LEVEL,
            "formatter": "detailed",
            "filename": settings.LOG_FILE_PATH,
            "maxBytes": 104857600,  # 100MB
            "backupCount": settings.LOG_BACKUP_COUNT,
            "encoding": "utf-8",
        },
    },
    "loggers": {
        "": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console", "file"],
            "propagate": False,
        },
        "uvicorn": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
        "sqlalchemy": {
            "level": "WARNING",
            "handlers": ["file"],
            "propagate": False,
        },
    },
}
